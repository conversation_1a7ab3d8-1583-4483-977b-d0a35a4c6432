#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的发现页图片更新脚本
直接使用新的图片池数据更新发现页的imageUrls字段
"""

import json
import random
from pathlib import Path
from image_pool_manager import ImagePoolManager

def update_discover_images():
    """更新发现页图片"""

    # 文件路径
    discover_file = "../../luyea/Resources/MockData/discover_list.json"

    # 检查文件是否存在
    if not Path(discover_file).exists():
        print(f"❌ 错误: 找不到发现列表文件 {discover_file}")
        return False

    # 创建图片池管理器
    image_manager = ImagePoolManager()

    print(f"📊 图片池包含 {len(image_manager.image_ids)} 个图片ID")
    
    # 读取发现列表
    with open(discover_file, 'r', encoding='utf-8') as f:
        discover_data = json.load(f)
    
    items = discover_data.get('items', [])
    print(f"📋 发现列表包含 {len(items)} 个项目")
    
    # 创建备份
    backup_file = f"{discover_file}.backup"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(discover_data, f, ensure_ascii=False, indent=2)
    print(f"💾 已创建备份文件: {backup_file}")
    
    # 图片类别权重（根据移动设备使用习惯）
    categories = ["mobile_portrait", "mobile_landscape", "square", "wide_panoramic"]
    category_weights = [0.4, 0.3, 0.2, 0.1]  # 移动纵向40%, 横向30%, 方形20%, 宽屏10%

    updated_count = 0
    total_images_used = 0

    print(f"\n🔄 开始更新图片...")

    for i, item in enumerate(items):
        # 为每个项目生成2-4张图片
        image_count = random.randint(2, 4)
        project_images = []

        for _ in range(image_count):
            # 根据权重随机选择图片类别
            category = random.choices(categories, weights=category_weights)[0]

            # 使用ImagePoolManager获取图片
            images = image_manager.get_images(count=1, category=category)

            if images:
                project_images.append(images[0]['url'])
                total_images_used += 1

        # 更新项目的图片URLs
        item['imageUrls'] = project_images
        updated_count += 1

        if (i + 1) % 20 == 0:
            print(f"   已处理 {i + 1}/{len(items)} 个项目...")
    
    # 保存更新后的发现列表
    with open(discover_file, 'w', encoding='utf-8') as f:
        json.dump(discover_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 更新完成!")
    print(f"✅ 更新了 {updated_count} 个项目")
    print(f"🖼️ 总共生成了 {total_images_used} 张图片")
    print(f"📊 平均每个项目 {total_images_used / len(items):.1f} 张图片")
    
    return True

if __name__ == "__main__":
    print("🖼️ 发现页图片更新工具")
    print("=" * 40)
    
    success = update_discover_images()
    
    if success:
        print("\n✅ 图片更新成功！")
    else:
        print("\n❌ 图片更新失败！")
