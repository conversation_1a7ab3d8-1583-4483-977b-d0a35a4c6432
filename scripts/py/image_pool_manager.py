#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unsplash图片池管理器
功能：管理图片ID池，动态生成URL，验证图片可访问性
"""

import json
import random
import time
import urllib.request
import ssl
import re
import argparse

def generate_image_url(image_id, width, height, quality=85):
    """根据图片ID和尺寸生成Unsplash URL"""
    return f"https://images.unsplash.com/photo-{image_id}?ixlib=rb-4.0.3&auto=format&fit=crop&w={width}&h={height}&q={quality}"

def extract_image_id(url):
    """从URL中提取图片ID"""
    match = re.search(r'photo-([a-zA-Z0-9_-]+)', url)
    if match:
        return match.group(1)
    return None

class ImagePoolManager:
    def __init__(self, ids_file="image_ids.json"):
        self.ids_file = ids_file
        self.image_ids = self.load_image_ids()

        # 图片尺寸配置
        self.size_configs = {
            "mobile_portrait": [{"w": 375, "h": 667, "ratio": "9:16"}, {"w": 414, "h": 736, "ratio": "9:16"}],
            "mobile_landscape": [{"w": 667, "h": 375, "ratio": "16:9"}, {"w": 736, "h": 414, "ratio": "16:9"}],
            "square": [{"w": 400, "h": 400, "ratio": "1:1"}, {"w": 600, "h": 600, "ratio": "1:1"}],
            "wide_panoramic": [{"w": 800, "h": 400, "ratio": "2:1"}, {"w": 1200, "h": 600, "ratio": "2:1"}]
        }

    def load_image_ids(self):
        """加载图片ID列表"""
        try:
            with open(self.ids_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return []

    def save_image_ids(self):
        """保存图片ID列表"""
        with open(self.ids_file, 'w', encoding='utf-8') as f:
            json.dump(self.image_ids, f, ensure_ascii=False, indent=2)

    def verify_image_id(self, image_id, timeout=10, show_error=False):
        """验证单个图片ID是否可访问"""
        test_url = generate_image_url(image_id, 400, 300)

        try:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            req = urllib.request.Request(test_url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')

            with urllib.request.urlopen(req, timeout=timeout, context=ssl_context) as response:
                if response.status == 200:
                    return True
                else:
                    if show_error:
                        print(f" [HTTP {response.status}]", end="")
                    return False

        except Exception as e:
            if show_error:
                error_type = type(e).__name__
                if "404" in str(e) or "Not Found" in str(e):
                    print(f" [404]", end="")
                elif "timeout" in str(e).lower():
                    print(f" [超时]", end="")
                elif "ssl" in str(e).lower():
                    print(f" [SSL]", end="")
                else:
                    print(f" [{error_type}]", end="")
            return False

    def get_images(self, count=3, category=None):
        """获取指定数量的图片"""
        if len(self.image_ids) < count:
            print(f"⚠️ 图片ID不足，需要 {count} 个，只有 {len(self.image_ids)} 个")
            count = len(self.image_ids)

        if count == 0:
            return []

        selected_ids = random.sample(self.image_ids, count)
        images = []

        for img_id in selected_ids:
            # 选择类别和尺寸
            selected_category = category or random.choice(list(self.size_configs.keys()))
            config = random.choice(self.size_configs[selected_category])

            images.append({
                "image_id": img_id,
                "url": generate_image_url(img_id, config["w"], config["h"]),
                "width": config["w"],
                "height": config["h"],
                "ratio": config["ratio"],
                "category": selected_category
            })

        return images



    def refresh_pool(self, target_count=50, append_mode=False):
        """刷新图片池"""
        if append_mode:
            # 追加模式：先验证和清理现有ID
            print(f"🔍 追加前先验证现有图片池...")
            valid_count, invalid_count = self.verify_and_clean_pool()

            if invalid_count > 0:
                print(f"📊 清理完成：移除 {invalid_count} 个无效ID，保留 {valid_count} 个有效ID")

            current_count = len(self.image_ids)
            needed = target_count - current_count
            if needed <= 0:
                print(f"✅ 已达到目标数量 {target_count}")
                return
            print(f"🔄 追加图片，当前有效: {current_count}，需要: {needed}")
            target_count = needed
        else:
            print(f"🔄 刷新图片池，目标: {target_count}")

        if append_mode:
            # 追加模式：传递现有ID，获取新的不重复ID
            new_ids = self.fetch_from_multiple_topics_with_existing(target_count, self.image_ids)
            unique_new_ids = [img_id for img_id in new_ids if img_id not in self.image_ids]
            self.image_ids.extend(unique_new_ids)
            print(f"✅ 追加完成! 新增 {len(unique_new_ids)} 个，总计 {len(self.image_ids)} 个")
        else:
            # 刷新模式：不考虑现有ID
            new_ids = self.fetch_from_multiple_topics(target_count)
            self.image_ids = new_ids
            print(f"✅ 刷新完成! 获得 {len(new_ids)} 个有效图片ID")

        self.save_image_ids()

    def fetch_from_multiple_topics(self, target_count=50):
        """从多个主题获取图片ID"""
        topics = ["travel", "nature", "architecture-interior", "film",
                 "technology", "street-photography", "wallpapers", "textures-patterns"]
        all_ids = []

        for topic in topics:
            if len(all_ids) >= target_count:
                break

            # 每个主题最多尝试100个ID，但如果有效ID达到目标就停止
            print(f"🌍 从 {topic} 主题获取图片 (最多200个)...")
            remaining_needed = target_count - len(all_ids)
            topic_ids = self.fetch_from_topic_with_limit(topic, 200, remaining_needed, all_ids)

            # 添加新的有效ID
            added_count = 0
            for img_id in topic_ids:
                if img_id not in all_ids and len(all_ids) < target_count:
                    all_ids.append(img_id)
                    added_count += 1
                    if len(all_ids) >= target_count:
                        break

            print(f"  ✅ {topic}: 新增 {added_count} 个，总计 {len(all_ids)} 个")

            if len(all_ids) >= target_count:
                print(f"🎯 已达到目标数量 {target_count}，停止获取")
                break

        return all_ids[:target_count]

    def fetch_from_multiple_topics_with_existing(self, target_count=50, existing_ids=[]):
        """从多个主题获取图片ID，跳过已存在的ID"""
        topics = ["travel", "nature", "architecture-interior", "film",
                 "technology", "street-photography", "wallpapers", "textures-patterns"]
        all_ids = []
        existing_set = set(existing_ids)

        for topic in topics:
            if len(all_ids) >= target_count:
                break

            # 每个主题最多尝试100个ID，跳过已存在的ID
            print(f"🌍 从 {topic} 主题获取图片 (最多100个，跳过重复)...")
            remaining_needed = target_count - len(all_ids)
            topic_ids = self.fetch_from_topic_with_limit(topic, 100, remaining_needed, existing_set.union(all_ids))

            # 添加新的有效ID
            added_count = 0
            for img_id in topic_ids:
                if img_id not in existing_set and img_id not in all_ids and len(all_ids) < target_count:
                    all_ids.append(img_id)
                    added_count += 1
                    if len(all_ids) >= target_count:
                        break

            print(f"  ✅ {topic}: 新增 {added_count} 个，总计 {len(all_ids)} 个")

            if len(all_ids) >= target_count:
                print(f"🎯 已达到目标数量 {target_count}，停止获取")
                break

        return all_ids[:target_count]

    def fetch_from_topic_with_limit(self, topic, max_attempts=100, max_valid_needed=50, existing_ids=[]):
        """从指定主题获取图片ID，在达到有效ID目标时停止"""
        valid_ids = []
        attempted_count = 0
        total_existing = len(existing_ids)

        per_page = 20
        max_pages = (max_attempts // per_page) + 2

        for page in range(1, max_pages + 1):
            if attempted_count >= max_attempts or len(valid_ids) >= max_valid_needed:
                break

            try:
                api_url = f"https://unsplash.com/napi/topics/{topic}/photos?page={page}&per_page={per_page}"

                req = urllib.request.Request(api_url)
                req.add_header('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')
                req.add_header('Accept', 'application/json')
                req.add_header('Referer', f'https://unsplash.com/t/{topic}')

                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

                response = urllib.request.urlopen(req, context=ssl_context, timeout=15)
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    photos = json.loads(content)

                    for photo in photos:
                        if attempted_count >= max_attempts or len(valid_ids) >= max_valid_needed:
                            break

                        if 'urls' in photo and 'raw' in photo['urls']:
                            raw_url = photo['urls']['raw']
                            image_id = extract_image_id(raw_url)

                            if image_id and image_id not in valid_ids and image_id not in existing_ids:
                                attempted_count += 1
                                current_total = total_existing + len(valid_ids)
                                print(f"🔍 验证 {attempted_count}/{max_attempts}: {image_id[:20]}... ", end="")
                                if self.verify_image_id(image_id, show_error=True):
                                    valid_ids.append(image_id)
                                    current_total += 1
                                    print(f"✅ (总有效: {current_total})")
                                    if len(valid_ids) >= max_valid_needed:
                                        print(f"  🎯 已获取足够的有效ID，停止此主题")
                                        break
                                else:
                                    print("❌")

                                time.sleep(0.1)

                    time.sleep(0.5)
                else:
                    print(f"⚠️ {topic} 第{page}页请求失败: HTTP {response.status}")

            except Exception as e:
                print(f"⚠️ 获取 {topic} 第{page}页时出错: {str(e)}")
                continue

        print(f"  {topic}: 尝试 {attempted_count} 个，获得 {len(valid_ids)} 个有效ID")
        return valid_ids

    def fetch_from_topic(self, topic, max_attempts=100):
        """从指定主题获取图片ID"""
        valid_ids = []
        attempted_ids = 0

        per_page = 20
        max_pages = (max_attempts // per_page) + 2

        for page in range(1, max_pages + 1):
            if attempted_ids >= max_attempts:
                break

            try:
                api_url = f"https://unsplash.com/napi/topics/{topic}/photos?page={page}&per_page={per_page}"

                req = urllib.request.Request(api_url)
                req.add_header('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')
                req.add_header('Accept', 'application/json')
                req.add_header('Referer', f'https://unsplash.com/t/{topic}')

                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

                response = urllib.request.urlopen(req, context=ssl_context, timeout=15)
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    photos = json.loads(content)

                    for photo in photos:
                        if attempted_ids >= max_attempts:
                            break

                        if 'urls' in photo and 'raw' in photo['urls']:
                            raw_url = photo['urls']['raw']
                            image_id = extract_image_id(raw_url)

                            if image_id and image_id not in valid_ids:
                                attempted_ids += 1
                                # 验证图片可访问性
                                print(f"🔍 验证 {attempted_ids}/{max_attempts}: {image_id[:20]}... ", end="")
                                if self.verify_image_id(image_id, show_error=True):
                                    valid_ids.append(image_id)
                                    print(f"✅ (有效: {len(valid_ids)})")
                                    if len(valid_ids) % 5 == 0:
                                        print(f"  ✅ 已获取 {len(valid_ids)} 个有效ID")
                                else:
                                    print("❌")

                                time.sleep(0.1)  # 减少延迟

                    time.sleep(0.5)  # 减少页面间延迟
                else:
                    print(f"⚠️ {topic} 第{page}页请求失败: HTTP {response.status}")

            except Exception as e:
                print(f"⚠️ 获取 {topic} 第{page}页时出错: {str(e)}")
                continue

        print(f"  {topic}: 尝试 {attempted_ids} 个，获得 {len(valid_ids)} 个有效ID")
        return valid_ids

    def verify_and_clean_pool(self):
        """验证并清理无效ID"""
        original_count = len(self.image_ids)
        print(f"🔍 验证 {original_count} 个图片ID...")

        valid_ids = []
        for i, img_id in enumerate(self.image_ids, 1):
            print(f"验证 {i}/{original_count}: {img_id[:20]}... ", end="")
            if self.verify_image_id(img_id, show_error=True):
                valid_ids.append(img_id)
                print("✅")
            else:
                print("❌")
            time.sleep(0.2)

        self.image_ids = valid_ids
        self.save_image_ids()

        invalid_count = original_count - len(valid_ids)
        print(f"\n📊 验证完成: ✅{len(valid_ids)} ❌{invalid_count}")
        return len(valid_ids), invalid_count

    def print_stats(self):
        """显示统计信息"""
        print(f"📊 图片池: {len(self.image_ids)} 个")
        print(f"📱 类别: {', '.join(self.size_configs.keys())}")

def main():
    parser = argparse.ArgumentParser(description="Unsplash图片池管理器")
    parser.add_argument("--test", type=int, metavar="N", help="测试获取N张图片")
    parser.add_argument("--category", choices=["mobile_portrait", "mobile_landscape", "square", "wide_panoramic"], help="指定图片类别")
    parser.add_argument("--refresh", type=int, metavar="N", help="刷新图片池，获取N张图片")
    parser.add_argument("--verify", action="store_true", help="验证并清理无效ID")
    parser.add_argument("--append", type=int, metavar="N", help="追加到N张图片")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")

    args = parser.parse_args()
    manager = ImagePoolManager()

    if args.test:
        images = manager.get_images(count=args.test, category=args.category)
        for i, img in enumerate(images, 1):
            print(f"{i}. {img['ratio']} ({img['width']}x{img['height']}) - {img['image_id'][:15]}...")
    elif args.verify:
        manager.verify_and_clean_pool()
    elif args.append:
        manager.refresh_pool(args.append, append_mode=True)
    elif args.refresh:
        manager.refresh_pool(args.refresh)
    elif args.stats:
        manager.print_stats()
    else:
        manager.print_stats()
        print("💡 使用 --help 查看帮助")

if __name__ == "__main__":
    main()
