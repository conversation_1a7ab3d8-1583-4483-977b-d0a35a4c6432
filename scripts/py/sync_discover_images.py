#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步发现页前三条数据的图片与发现详情页
"""

import json
import os

def sync_images():
    """同步发现列表和详情页的图片"""
    
    base_path = "../../luyea/Resources/MockData"
    
    # 读取发现列表数据
    list_file = f"{base_path}/discover_list.json"
    with open(list_file, 'r', encoding='utf-8') as f:
        discover_list = json.load(f)
    
    # 详情页文件
    detail_files = [
        f"{base_path}/api_v1_discover_1_detail.json",
        f"{base_path}/api_v1_discover_2_detail.json", 
        f"{base_path}/api_v1_discover_3_detail.json"
    ]
    
    print("🔄 开始将发现列表的图片同步到详情页...")
    print()
    
    for i in range(3):
        list_item = discover_list['items'][i]
        detail_file = detail_files[i]
        
        # 读取详情页数据
        with open(detail_file, 'r', encoding='utf-8') as f:
            detail_data = json.load(f)
        
        # 获取列表页的图片URLs
        list_image_urls = list_item['imageUrls']

        print(f"📋 第{i+1}个发现：{list_item['title']}")
        print(f"   列表页图片数量: {len(list_image_urls)}")
        print(f"   详情页原图片数量: {len(detail_data['images'])}")

        # 更新详情页的图片数据（保持原有的id和type结构）
        new_images = []
        for j, url in enumerate(list_image_urls):
            new_images.append({
                "id": f"img_{i+1}_{j+1}",
                "url": url,
                "type": "image"
            })

        detail_data['images'] = new_images

        # 保存更新后的详情页数据
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump(detail_data, f, ensure_ascii=False, indent=2)
        
        print(f"   ✅ 已同步 {len(list_image_urls)} 张图片到详情页")
        print()
    
    print("🎉 图片同步完成！")
    return True

def verify_sync():
    """验证同步结果"""
    
    base_path = "../../luyea/Resources/MockData"
    
    # 读取发现列表数据
    with open(f"{base_path}/discover_list.json", 'r', encoding='utf-8') as f:
        discover_list = json.load(f)
    
    # 详情页文件
    detail_files = [
        f"{base_path}/api_v1_discover_1_detail.json",
        f"{base_path}/api_v1_discover_2_detail.json", 
        f"{base_path}/api_v1_discover_3_detail.json"
    ]
    
    print("\n📊 验证同步结果：")
    print()
    
    all_synced = True
    
    for i in range(3):
        list_item = discover_list['items'][i]
        
        with open(detail_files[i], 'r', encoding='utf-8') as f:
            detail_data = json.load(f)
        
        list_urls = list_item['imageUrls']
        detail_urls = [img['url'] for img in detail_data['images']]
        
        is_synced = list_urls == detail_urls
        status = "✅ 已同步" if is_synced else "❌ 未同步"
        
        print(f"🔍 第{i+1}个发现：{list_item['title']}")
        print(f"   状态: {status}")
        print(f"   图片数量: 列表页 {len(list_urls)} | 详情页 {len(detail_urls)}")
        
        if not is_synced:
            all_synced = False
            print(f"   ⚠️  图片不匹配")
        
        print()
    
    if all_synced:
        print("🎉 所有图片已成功同步！")
    else:
        print("⚠️  部分图片同步失败，请检查")
    
    return all_synced

def main():
    """主函数"""
    print("🖼️ 发现页图片同步工具")
    print("=" * 40)
    
    try:
        # 执行同步
        if sync_images():
            # 验证结果
            verify_sync()
        else:
            print("❌ 图片同步失败")
            
    except Exception as e:
        print(f"❌ 同步过程中出现错误: {e}")

if __name__ == "__main__":
    main()
