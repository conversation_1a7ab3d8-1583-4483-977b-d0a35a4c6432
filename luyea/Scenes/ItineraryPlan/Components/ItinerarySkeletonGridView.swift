import SwiftUI

/// ItinerarySkeletonGridView
/// 行程卡片骨架屏加载视图，支持深色模式和浅色模式自适应
struct ItinerarySkeletonGridView: View {
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 10), count: 2), spacing: 10) {
            ForEach(0..<6, id: \.self) { _ in
                itinerarySkeletonCard
            }
        }
        .padding(.horizontal, 10)
        .padding(.top, 16)
    }

    /// 行程骨架卡片
    private var itinerarySkeletonCard: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题区域
            RoundedRectangle(cornerRadius: 4)
                .fill(primarySkeletonColor)
                .frame(height: 16)
                .frame(maxWidth: .infinity)

            // 副标题区域
            RoundedRectangle(cornerRadius: 3)
                .fill(secondarySkeletonColor)
                .frame(height: 12)
                .frame(maxWidth: titleWidth)

            Spacer()

            // 底部信息区域
            HStack {
                RoundedRectangle(cornerRadius: 3)
                    .fill(secondarySkeletonColor)
                    .frame(width: 40, height: 10)

                Spacer()

                RoundedRectangle(cornerRadius: 3)
                    .fill(secondarySkeletonColor)
                    .frame(width: 30, height: 10)
            }
        }
        .padding(12)
        .frame(height: 90)
        .background(cardBackgroundColor)
        .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
        .shadow(
            color: shadowColor,
            radius: 4,
            x: 0,
            y: 2
        )
        .shimmer(isActive: true)
    }

    // MARK: - 颜色计算属性

    /// 卡片背景色
    private var cardBackgroundColor: Color {
        Color(.systemBackground)
    }

    /// 阴影颜色
    private var shadowColor: Color {
        switch colorScheme {
        case .light:
            return Color.primary.opacity(0.06)
        case .dark:
            return Color.black.opacity(0.2)
        @unknown default:
            return Color.primary.opacity(0.06)
        }
    }

    /// 主要骨架颜色
    private var primarySkeletonColor: Color {
        switch colorScheme {
        case .light:
            return Color(.systemGray5).opacity(0.8)
        case .dark:
            return Color(.systemGray4).opacity(0.6)
        @unknown default:
            return Color(.systemGray5).opacity(0.8)
        }
    }

    /// 次要骨架颜色
    private var secondarySkeletonColor: Color {
        switch colorScheme {
        case .light:
            return Color(.systemGray6).opacity(0.7)
        case .dark:
            return Color(.systemGray5).opacity(0.5)
        @unknown default:
            return Color(.systemGray6).opacity(0.7)
        }
    }

    /// 标题宽度
    private var titleWidth: CGFloat {
        let ratios: [CGFloat] = [0.6, 0.7, 0.8]
        return ratios.randomElement() ?? 0.7
    }
}

#Preview("浅色模式") {
    ItinerarySkeletonGridView()
        .background(Color(.systemGroupedBackground))
        .preferredColorScheme(.light)
}

#Preview("深色模式") {
    ItinerarySkeletonGridView()
        .background(Color(.systemGroupedBackground))
        .preferredColorScheme(.dark)
}