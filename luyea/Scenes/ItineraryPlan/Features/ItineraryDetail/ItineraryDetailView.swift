import SwiftUI

/// 行程卡片详情视图（展开后）
/// 展示预计花费、里程、流量反馈/预测、备注等信息
struct ItineraryDetailView: View {
    let itinerary: ItineraryModel
    @State private var showFeedbackSheet = false
    @State private var showThankYouAlert = false


    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 信息卡片区
            HStack(spacing: 16) {
                InfoCard(icon: "creditcard.fill", iconColor: .blue, label: "预计花费", value: "¥\(Int(itinerary.estimatedCost))")
                InfoCard(icon: "map.fill", iconColor: .green, label: "跨越里程", value: "\(Int(itinerary.distance))公里")
                // 流量反馈/预测
                if itinerary.status == "已结束" {
                    Button(action: handleTrafficFeedbackTap) {
                        InfoCard(
                            icon: "chart.bar.fill",
                            iconColor: .orange,
                            label: "流量峰值",
                            value: itinerary.trafficFeedback ?? itinerary.trafficForecast
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .accessibilityLabel(itinerary.trafficFeedback == nil ? "提交流量反馈" : "已反馈")
                    .sheet(isPresented: $showFeedbackSheet) {
                        TrafficFeedbackSheet(onSubmit: handleFeedbackSubmit)
                    }
                } else {
                    InfoCard(icon: "chart.bar.fill", iconColor: .orange, label: "流量预测", value: itinerary.trafficForecast)
                }
            }
            .padding(.vertical, 2)

            Divider()

            // 备注区
            VStack(alignment: .leading, spacing: 8) {
                Text("行程备注")
                    .font(.system(size: 13, weight: .bold))
                    .foregroundColor(.secondary)
                Text("这里可以对你的行程进行描述，备注提醒等。")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
            }
        }
        .padding(DesignSystemConstants.Spacing.standard)
        .background(
            RoundedRectangle(cornerRadius: 18, style: .continuous)
                .fill(.ultraThinMaterial)
                .shadow(color: Color.black.opacity(0.14), radius: 10, x: 0, y: 5)
        )
        .padding(.top, 6)
        .alert(isPresented: $showThankYouAlert) {
            Alert(title: Text("感谢反馈"), message: Text("您的流量情况反馈已收到。"), dismissButton: .default(Text("确定")))
        }

    }

    /// 处理流量反馈卡片点击
    private func handleTrafficFeedbackTap() {
        if itinerary.trafficFeedback == nil {
            showFeedbackSheet = true
        } else {
            ToastManager.shared.show("已提交过反馈啦", style: .info)
        }
    }

    /// 处理反馈提交
    private func handleFeedbackSubmit(_ level: String) {
        NotificationCenter.default.post(name: .trafficFeedbackSubmitted, object: nil, userInfo: ["id": itinerary.id, "level": level])
        showFeedbackSheet = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            showThankYouAlert = true
        }
    }
}

// MARK: - InfoCard

/// 详情区小卡片，展示单项信息
private struct InfoCard: View {
    let icon: String
    let iconColor: Color
    let label: String
    let value: String
    var body: some View {
        VStack(spacing: 6) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 22, height: 22)
                Text(label)
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                    .truncationMode(.tail)
            }
            Text(value)
                .font(.system(size: 14, weight: .semibold, design: .rounded))
                .foregroundColor(.primary)
                .lineLimit(1)
                .truncationMode(.tail)
        }
        .frame(minWidth: 0, maxWidth: .infinity)
        .padding(.vertical, 10)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12, style: .continuous)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - TrafficFeedbackSheet

/// 流量反馈弹窗 - 简约统一风格
private struct TrafficFeedbackSheet: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedLevel: String = ""
    @State private var comment: String = ""
    let onSubmit: (String) -> Void

    var body: some View {
        VStack(spacing: 24) {
            // 标题区域 - 简约设计
            VStack(spacing: 12) {
                Image(systemName: "chart.bar.fill")
                    .font(.system(size: 28, weight: .medium))
                    .foregroundColor(.blue)
                    .padding(.top, 8)

                Text("反馈实际流量情况")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
            }
            // 选项区域 - 极简紧凑设计
            HStack(spacing: 8) {
                ForEach(trafficLevels, id: \.self) { level in
                    Button(action: {
                        // 添加触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                        selectedLevel = level
                    }) {
                        Text(level)
                            .font(.system(size: 14, weight: selectedLevel == level ? .semibold : .medium))
                            .foregroundColor(selectedLevel == level ? .white : .primary)
                            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(selectedLevel == level ? levelColor(for: level) : Color(.systemBackground))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(selectedLevel == level ? Color.clear : Color(.systemGray4), lineWidth: 0.5)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            // 输入框区域 - 简约设计
            VStack(alignment: .leading, spacing: 8) {
                Text("补充说明（可选）")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)

                ZStack {
                    // 背景容器
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemBackground))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                        )
                        .frame(minHeight: 60, maxHeight: 80)

                    // TextEditor
                    TextEditor(text: $comment)
                        .font(.system(size: 15))
                        .foregroundColor(.primary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 12)
                        .background(Color.clear)
                        .frame(minHeight: 60, maxHeight: 80)
                        .overlay(
                            // 占位符文本
                            Group {
                                if comment.isEmpty {
                                    HStack {
                                        VStack(alignment: .leading) {
                                            Text("例如：高峰期拥堵严重，建议避开早晚高峰出行")
                                                .font(.system(size: 15))
                                                .foregroundColor(.secondary)
                                                .multilineTextAlignment(.leading)
                                            Spacer()
                                        }
                                        Spacer()
                                    }
                                    .padding(.horizontal, DesignSystemConstants.Spacing.standard)
                                    .padding(.vertical, DesignSystemConstants.Spacing.standard)
                                    .allowsHitTesting(false)
                                }
                            },
                            alignment: .topLeading
                        )
                }
            }
            Spacer(minLength: 16)

            // 提交按钮 - 简约设计
            Button(action: {
                // 添加触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                dismiss()
                onSubmit(selectedLevel)
            }) {
                Text("提交反馈")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, DesignSystemConstants.Spacing.standard)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedLevel.isEmpty ? Color.gray.opacity(0.3) : Color.blue)
                    )
            }
            .disabled(selectedLevel.isEmpty)
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
        .presentationDetents([.height(420)])
        .presentationDragIndicator(.visible)
    }
}

// MARK: - 常量与工具方法

/// 流量反馈选项
private let trafficLevels = ["高峰", "中等", "低"]

/// 获取流量等级对应的图标
private func levelIcon(for level: String) -> String {
    switch level {
    case "高峰": return "arrow.up.circle.fill"
    case "中等": return "minus.circle.fill"
    case "低": return "arrow.down.circle.fill"
    default: return "circle.fill"
    }
}

/// 获取流量等级对应的颜色
private func levelColor(for level: String) -> Color {
    switch level {
    case "高峰": return .red
    case "中等": return .orange
    case "低": return .green
    default: return .gray
    }
}

// MARK: - 通知说明
// trafficFeedbackSubmitted 通知已在 ItineraryPlanViewModel.swift 中定义