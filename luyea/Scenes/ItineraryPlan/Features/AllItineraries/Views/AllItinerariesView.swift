import SwiftUI

/// AllItinerariesView
/// 展示全部行程卡片的页面，支持分页加载、下拉刷新、上拉加载
struct AllItinerariesView: View {
    @StateObject private var viewModel = AllItinerariesViewModel()
    @State private var expandedId: String? = nil
    @State private var showCustomFilterMenu: Bool = false
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 14) {
                    // 滚动锚点
                    Color.clear
                        .frame(height: 1)
                        .id("top")
                    
                    // 行程卡片列表
                    ForEach(viewModel.filteredItineraries, id: \.id) { itinerary in
                        LazyView {
                            ItineraryCardView(
                                itinerary: itinerary,
                                isExpanded: expandedId == itinerary.id,
                                onTap: {
                                    // 添加触觉反馈
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                    impactFeedback.impactOccurred()
                                    
                                    withAnimation(.spring(response: 0.45, dampingFraction: 0.85)) {
                                        expandedId = (expandedId == itinerary.id) ? nil : itinerary.id
                                    }
                                },
                                showInfoButton: itinerary.id == viewModel.filteredItineraries.first?.id
                            )
                        }
                        .id(itinerary.id)
                        .onAppear {
                            // 检查是否需要加载更多
                            viewModel.checkForLoadMore(currentItem: itinerary)
                        }
                    }
                    
                    // 底部加载状态
                    bottomLoadingView
                }
                .padding(.horizontal, 16)
            }
            .refreshable {
                await viewModel.refreshData()
            }
            .onChange(of: viewModel.selectedStatus) { _, _ in
                // 筛选状态改变时滚动到顶部
                withAnimation(.easeInOut(duration: 0.5)) {
                    proxy.scrollTo("top", anchor: .top)
                }
            }
        }
        .appBackground()
        .navigationTitle(navigationTitle)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .enableSwipeBackGesture()
        .hideTabBar()
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: DesignSystemConstants.Navigation.backButtonIconSize,
                                     weight: DesignSystemConstants.Navigation.backButtonFontWeight))
                        .frame(width: 32, height: 32)
                        .contentShape(Rectangle())
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        showCustomFilterMenu.toggle()
                    }
                }) {
                    Image(systemName: "line.3.horizontal.decrease")
                        .frame(width: 32, height: 32)
                        .contentShape(Rectangle())
                }
            }
        }
        .overlay {
            // 菜单覆盖层
            if showCustomFilterMenu {
                ZStack {
                    // 背景遮罩（只覆盖内容区域）
                    Color.black.opacity(0.3)
                        .ignoresSafeArea(edges: [.bottom, .horizontal])
                        .onTapGesture {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                showCustomFilterMenu = false
                            }
                        }

                    // 菜单内容
                    VStack {
                        HStack {
                            Spacer()
                            customFilterMenu
                                .padding(.trailing, 16) // 右侧边距
                        }
                        .padding(.top, 8) // 紧贴导航栏
                        Spacer()
                    }
                }
                .transition(.opacity)
            }
        }
        .onAppear {
            if viewModel.filteredItineraries.isEmpty {
                viewModel.loadInitialData()
            }
        }
        .alert("加载失败", isPresented: $viewModel.showErrorAlert) {
            Button("重试") {
                viewModel.loadInitialData()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text(viewModel.error?.localizedDescription ?? "未知错误")
        }
    }
}

// MARK: - Private Views
extension AllItinerariesView {
    /// 动态导航栏标题
    private var navigationTitle: String {
        if viewModel.selectedStatus == "全部" {
            return "全部行程"
        } else {
            return "全部行程 · \(viewModel.selectedStatus)"
        }
    }
    /// 自定义筛选菜单（模拟系统弹出方式）
    private var customFilterMenu: some View {
        VStack(spacing: 0) {
            // 菜单头部
            menuHeader

            // 菜单选项
            VStack(spacing: 4) {
                ForEach(viewModel.statusOptions, id: \.self) { status in
                    menuItem(for: status)
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.15), radius: 20, x: 0, y: 8)
        )
        .frame(width: 220)
        // 模拟系统Menu的弹出效果
        .scaleEffect(showCustomFilterMenu ? 1.0 : 0.8, anchor: .topTrailing)
        .opacity(showCustomFilterMenu ? 1.0 : 0.0)
        .animation(
            .interpolatingSpring(stiffness: 400, damping: 22),
            value: showCustomFilterMenu
        )
    }

    /// 菜单头部
    private var menuHeader: some View {
        HStack(spacing: 8) {
            Image(systemName: "line.3.horizontal.decrease")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.blue)

            Text("筛选行程")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.primary)

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            Rectangle()
                .fill(Color(.systemGray6).opacity(0.5))
        )
        .clipShape(
            .rect(
                topLeadingRadius: 16,
                topTrailingRadius: 16
            )
        )
    }

    /// 菜单项
    private func menuItem(for status: String) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                viewModel.updateStatusFilter(status)
                showCustomFilterMenu = false
            }
        }) {
            HStack(spacing: 12) {
                // 状态图标
                ZStack {
                    Circle()
                        .fill(statusColor(for: status).opacity(0.15))
                        .frame(width: 32, height: 32)

                    Image(systemName: statusIcon(for: status))
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(statusColor(for: status))
                }

                // 状态文本
                VStack(alignment: .leading, spacing: 2) {
                    Text(status)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(.primary)

                    Text(statusDescription(for: status))
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 选中标识
                if viewModel.selectedStatus == status {
                    ZStack {
                        Circle()
                            .fill(.blue)
                            .frame(width: 20, height: 20)

                        Image(systemName: "checkmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 12, style: .continuous)
                    .fill(viewModel.selectedStatus == status ?
                          Color.blue.opacity(0.08) : Color.clear)
            )
        }
        .buttonStyle(CustomMenuItemButtonStyle())
    }

    /// 筛选菜单内容（保留用于兼容性）
    @ViewBuilder
    private var filterMenuContent: some View {
        // 菜单标题
        Label("筛选行程", systemImage: "line.3.horizontal.decrease")
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.primary)

        Divider()

        // 筛选选项
        ForEach(viewModel.statusOptions, id: \.self) { status in
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    viewModel.updateStatusFilter(status)
                }
            }) {
                HStack(spacing: 12) {
                    // 状态图标
                    Image(systemName: statusIcon(for: status))
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(statusColor(for: status))
                        .frame(width: 20)

                    // 状态文本
                    Text(status)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)

                    Spacer()

                    // 选中标识
                    if viewModel.selectedStatus == status {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.blue)
                    }
                }
                .padding(.vertical, 4)
            }
            .buttonStyle(CustomMenuItemButtonStyle())
        }
    }

    /// 获取状态对应的图标
    private func statusIcon(for status: String) -> String {
        switch status {
        case "全部":
            return "list.bullet"
        case "在途中":
            return "airplane"
        case "待出行":
            return "calendar.badge.clock"
        case "已结束":
            return "checkmark.circle"
        default:
            return "circle"
        }
    }

    /// 获取状态对应的颜色
    private func statusColor(for status: String) -> Color {
        switch status {
        case "全部":
            return .primary
        case "在途中":
            return ItineraryPlanConstants.StatusColors.inProgress
        case "待出行":
            return ItineraryPlanConstants.StatusColors.upcoming
        case "已结束":
            return ItineraryPlanConstants.StatusColors.completed
        default:
            return .primary
        }
    }

    /// 获取状态对应的描述
    private func statusDescription(for status: String) -> String {
        switch status {
        case "全部":
            return "查看所有行程"
        case "在途中":
            return "正在进行的行程"
        case "待出行":
            return "待出行、待规划的行程"
        case "已结束":
            return "已完成的行程"
        default:
            return ""
        }
    }
    
    /// 底部加载状态视图
    @ViewBuilder
    private var bottomLoadingView: some View {
        if viewModel.isLoadingMore {
            // 正在加载更多
            HStack {
                ProgressView()
                    .scaleEffect(0.8)
                Text("加载中...")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
            .frame(height: 50)
            
        } else if !viewModel.hasMoreData && !viewModel.filteredItineraries.isEmpty {
            // 没有更多数据
            Text("没有更多了")
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .frame(height: 50)
                
        } else if viewModel.isLoading && viewModel.filteredItineraries.isEmpty {
            // 首次加载
            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.2)
                Text("加载中...")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, minHeight: 200)
            
        } else if viewModel.filteredItineraries.isEmpty && !viewModel.isLoading {
            // 空状态
            VStack(spacing: 16) {
                Image(systemName: viewModel.selectedStatus == "全部" ? "tray" : "line.3.horizontal.decrease.circle")
                    .font(.system(size: 48))
                    .foregroundColor(.secondary)
                
                Text(viewModel.selectedStatus == "全部" ? "暂无行程" : "暂无\(viewModel.selectedStatus)行程")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(viewModel.selectedStatus == "全部" ? "下拉刷新试试" : "试试其他筛选条件")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, minHeight: 200)
        }
    }
}

// MARK: - Custom Menu Button Style
/// 自定义菜单项按钮样式
private struct CustomMenuItemButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: configuration.isPressed)
    }
}
