import Foundation
import SwiftUI
import Combine

/// 新建行程内容管理器
///
/// 统一管理新建行程模块的UI状态和用户交互，包括Tab导航、底部弹窗、
/// 拖拽手势、加载状态等。采用单一职责原则，简化架构设计。
///
/// 主要职责：
/// - Tab导航状态管理
/// - 底部弹窗状态和拖拽交互
/// - UI反馈（Toast、错误提示、加载状态）
/// - 头部导航控制
@MainActor
final class ContentManager: ObservableObject {

    // MARK: - Tab & Bottom Sheet Management

    /// 当前选中的Tab
    @Published var currentTab: TabModel = .create

    /// 底部弹窗状态 - 默认展开状态
    @Published var bottomSheetState: BottomSheetSnapState = .full

    /// 当前偏移量
    @Published var offsetY: CGFloat = 0

    /// 是否正在拖拽
    @Published var isDragging = false

    /// 是否正在动画
    @Published var isAnimating = false

    // MARK: - Overlay Management

    /// 头部导航是否可见
    @Published var isHeaderVisible = true

    /// 是否显示加载覆盖
    @Published var showLoadingOverlay = false

    /// 是否显示错误提示
    @Published var showErrorAlert = false

    /// 当前错误信息
    @Published var currentError: Error?

    /// 头部标题
    @Published var headerTitle: String = ""

    /// 头部副标题
    @Published var headerSubtitle: String?

    // MARK: - General State
    
    /// 内容管理器是否就绪
    @Published var isReady = true
    
    /// 是否正在加载
    @Published var isLoading = false
    
    // MARK: - Private Properties
    
    /// 订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 几何信息
    private var geometry: GeometryProxy?

    /// 拖拽开始时的偏移量
    private var dragStartOffset: CGFloat = 0

    /// 加载计时器
    private var loadingTimer: Timer?
    
    // MARK: - Initialization
    
    init() {
        setupObservation()
        Log.info("📱 新建行程内容管理器初始化完成")
    }

    deinit {
        loadingTimer?.invalidate()
        Log.debug("📱 新建行程内容管理器已销毁")
    }
    
    // MARK: - Public Methods - Lifecycle

    /// 初始化内容管理器 - 简化版本
    func initialize() {
        Log.info("📱 内容管理器初始化完成")
        isReady = true
    }
    
    /// 重置所有状态
    func reset() {
        // Tab & Bottom Sheet
        currentTab = .create
        bottomSheetState = .full
        offsetY = 0
        isDragging = false
        isAnimating = false

        // Overlay
        isHeaderVisible = true
        showLoadingOverlay = false
        showErrorAlert = false
        currentError = nil
        headerTitle = ""
        headerSubtitle = nil

        // General
        isReady = false
        isLoading = false

        // 清理计时器
        loadingTimer?.invalidate()
        loadingTimer = nil

        Log.debug("📱 内容管理器状态已重置")
    }
    
    /// 成为活跃状态
    func becomeActive() {
        Log.debug("📱 内容管理器变为活跃")
    }
}

// MARK: - Tab Management
extension ContentManager {
    
    /// 切换到指定Tab
    func switchToTab(_ tab: TabModel, animated: Bool = true) {
        guard tab != currentTab else { return }

        Log.debug("📱 切换Tab: \(currentTab.rawValue) -> \(tab.rawValue)")

        let animation: Animation? = animated ? DesignSystemConstants.standardEaseAnimation : nil

        withAnimation(animation) {
            currentTab = tab
        }
    }
    
    /// 更新底部弹窗状态
    func updateBottomSheetState(_ state: BottomSheetSnapState, animated: Bool = true) {
        guard state != bottomSheetState else { return }

        let newOffset = offsetForState(state)

        if animated {
            withAnimation(DesignSystemConstants.standardSpringAnimation) {
                bottomSheetState = state
                offsetY = newOffset
                isAnimating = true
            }

            // 使用动画完成监听替代硬编码延迟
            scheduleAnimationCompletion()
        } else {
            bottomSheetState = state
            offsetY = newOffset
        }

        Log.debug("📱 底部弹窗状态更新: \(state)")
    }

    /// 更新几何信息
    func updateGeometry(_ geometry: GeometryProxy) {
        self.geometry = geometry
        updateOffsetForCurrentState()
    }

    /// 处理拖拽开始
    func handleDragStart() {
        isDragging = true
        dragStartOffset = offsetY

        Log.debug("📱 开始拖拽弹窗")
    }

    /// 处理拖拽变化
    func handleDragChange(_ translation: CGFloat) {
        guard isDragging else { return }

        let newOffset = dragStartOffset + translation

        // 简化的拖拽约束
        offsetY = constrainOffset(newOffset)
    }

    /// 处理拖拽结束
    func handleDragEnd(velocity: CGFloat) {
        guard isDragging else { return }

        isDragging = false

        let targetState = determineTargetState(
            currentOffset: offsetY,
            velocity: velocity
        )

        // 强制吸附到目标状态，即使状态相同也要执行动画
        snapToState(targetState)

        Log.debug("📱 拖拽结束，目标状态: \(targetState)")
    }

    /// 强制吸附到指定状态（无论当前状态如何）
    private func snapToState(_ state: BottomSheetSnapState) {
        let targetOffset = offsetForState(state)

        // 强制执行吸附动画，即使当前偏移量接近目标偏移量
        withAnimation(DesignSystemConstants.standardSpringAnimation) {
            bottomSheetState = state
            offsetY = targetOffset
            isAnimating = true
        }

        scheduleAnimationCompletion()

        Log.debug("📱 强制吸附到状态: \(state), 偏移量: \(targetOffset)")
    }

}



// MARK: - Overlay Management
extension ContentManager {
    
    /// 显示头部导航
    func showHeader(animated: Bool = true) {
        updateHeaderVisibility(true, animated: animated)
    }

    /// 隐藏头部导航
    func hideHeader(animated: Bool = true) {
        updateHeaderVisibility(false, animated: animated)
    }

    /// 更新头部导航可见性 - 私有辅助方法
    private func updateHeaderVisibility(_ isVisible: Bool, animated: Bool) {
        let animation: Animation? = animated ? DesignSystemConstants.standardEaseAnimation : nil

        withAnimation(animation) {
            isHeaderVisible = isVisible
        }
    }
    
    /// 设置头部标题
    func setHeaderTitle(_ title: String, subtitle: String? = nil) {
        headerTitle = title
        headerSubtitle = subtitle
    }
    
    /// 显示加载覆盖
    func showLoading(message: String? = nil, timeout: TimeInterval? = nil) {
        withAnimation(.easeInOut(duration: NewItineraryConstants.Timing.loadingOverlayAnimationDuration)) {
            showLoadingOverlay = true
        }
        
        // 设置超时
        if let timeout = timeout {
            loadingTimer?.invalidate()
            loadingTimer = Timer.scheduledTimer(withTimeInterval: timeout, repeats: false) { [weak self] _ in
                Task { @MainActor in
                    self?.hideLoading()
                }
            }
        }
        
        Log.debug("🔝 显示加载覆盖: \(message ?? "无消息")")
    }
    
    /// 隐藏加载覆盖
    func hideLoading() {
        withAnimation(.easeInOut(duration: NewItineraryConstants.Timing.loadingOverlayAnimationDuration)) {
            showLoadingOverlay = false
        }

        loadingTimer?.invalidate()
        loadingTimer = nil

        Log.debug("🔝 隐藏加载覆盖")
    }

    /// 显示错误提示
    func showError(_ error: Error) {
        currentError = error
        showErrorAlert = true

        Log.error("🔝 显示错误: \(error.localizedDescription)")
    }

    /// 隐藏错误信息
    func hideError() {
        currentError = nil
        showErrorAlert = false
        Log.debug("🔝 隐藏错误信息")
    }

    // MARK: - Private Methods - Bottom Sheet

    /// 更新当前状态的偏移量
    private func updateOffsetForCurrentState() {
        let targetOffset = offsetForState(bottomSheetState)
        if !isDragging && !isAnimating {
            offsetY = targetOffset
        }
    }

    /// 获取指定状态的偏移量
    private func offsetForState(_ state: BottomSheetSnapState) -> CGFloat {
        guard let geometry = geometry else { return 0 }

        let screenHeight = geometry.size.height

        switch state {
        case .full:
            // 展开时停在返回按钮底部
            return NewItineraryConstants.Layout.topClearance
        case .collapsed:
            // 收起时显示底部预览区域，忽略安全区域延伸到屏幕底部
            return screenHeight - NewItineraryConstants.Layout.collapsedSheetHeight
        }
    }

    /// 限制偏移量范围
    private func constrainOffset(_ offset: CGFloat) -> CGFloat {
        guard geometry != nil else { return offset }

        let minOffset = offsetForState(.full)
        let maxOffset = offsetForState(.collapsed)

        return max(minOffset, min(maxOffset, offset))
    }

    /// 确定目标状态
    private func determineTargetState(currentOffset: CGFloat, velocity: CGFloat) -> BottomSheetSnapState {
        let fullOffset = offsetForState(.full)
        let collapsedOffset = offsetForState(.collapsed)
        let midPoint = (fullOffset + collapsedOffset) / 2

        // 基于速度的智能判断
        if abs(velocity) > NewItineraryConstants.Threshold.velocityThreshold {
            return velocity > 0 ? .collapsed : .full
        }

        // 基于位置的判断
        return currentOffset < midPoint ? .full : .collapsed
    }

    /// 安排动画完成回调
    private func scheduleAnimationCompletion() {
        DispatchQueue.main.asyncAfter(deadline: .now() + DesignSystemConstants.Animation.slow) {
            self.isAnimating = false
        }
    }

}



// MARK: - Private Methods
private extension ContentManager {

    /// 设置观察
    func setupObservation() {
        // 简化观察逻辑，移除不必要的处理方法
        Log.debug("📱 内容管理器观察设置完成")
    }


}
