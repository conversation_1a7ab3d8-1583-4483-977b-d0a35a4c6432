import Foundation
import SwiftUI

/// 新建行程内容视图
struct NewItineraryContentView: View {
    @ObservedObject var contentManager: ContentManager
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        ZStack {
            if contentManager.isReady {
                contentContainer
                    .transition(.move(edge: .bottom).combined(with: .opacity))
            }

            if contentManager.showLoadingOverlay {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()

                VStack {
                    ProgressView()
                        .scaleEffect(1.2)

                    Text("加载中...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding(.top, 8)
                }
                .transition(.opacity)
            }
        }
        .alert("错误", isPresented: $contentManager.showErrorAlert) {
            Button("确定") {
                contentManager.hideError()
            }
        } message: {
            if let error = contentManager.currentError {
                Text(error.localizedDescription)
            }
        }
    }

    private var contentContainer: some View {
        BottomSheetView(contentManager: contentManager) {
            ItineraryCreationView(contentManager: contentManager)
        }
    }
}

/// 行程创建主视图
struct ItineraryCreationView: View {
    @ObservedObject var contentManager: ContentManager

    var body: some View {
        VStack(spacing: DesignSystemConstants.Spacing.standard) {
            tabNavigation
            tabContentView
                .padding(.horizontal, DesignSystemConstants.Spacing.standard)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .compositingGroup()
    }

    private var tabNavigation: some View {
        HStack(spacing: DesignSystemConstants.Spacing.large) {
            ForEach(TabModel.allCases) { tab in
                customTabItem(for: tab)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.large, style: .continuous))
        .shadow(color: .black.opacity(0.05), radius: DesignSystemConstants.Shadow.standard.radius, x: DesignSystemConstants.Shadow.standard.offset.width, y: DesignSystemConstants.Shadow.standard.offset.height)
        .padding(.horizontal, DesignSystemConstants.Spacing.standard)
    }

    @ViewBuilder
    private func customTabItem(for tab: TabModel) -> some View {
        let isSelected = contentManager.currentTab == tab
        let iconName = isSelected ? tab.selectedIconName : tab.iconName
        let accentColor = tab.accentColor

        Button(action: { selectTab(tab) }) {
            VStack(spacing: 2) {
                HStack(spacing: 6) {
                    Image(systemName: iconName)
                        .font(.system(size: 13, weight: isSelected ? .semibold : .regular))
                        .foregroundColor(isSelected ? accentColor : .secondary)

                    Text(tab.rawValue)
                        .font(isSelected ? .system(size: 15, weight: .bold) : .system(size: 15, weight: .regular))
                        .foregroundColor(isSelected ? accentColor : .secondary)
                        .lineLimit(1)
                        .fixedSize(horizontal: true, vertical: false)
                }

                if isSelected {
                    RoundedRectangle(cornerRadius: 1)
                        .fill(accentColor)
                        .frame(height: 2)
                } else {
                    RoundedRectangle(cornerRadius: 1)
                        .fill(.clear)
                        .frame(height: 2)
                }
            }
            .padding(.vertical, 4)
            .padding(.horizontal, 4)
        }
        .buttonStyle(.plain)
    }

    private func selectTab(_ tab: TabModel) {
        guard tab != contentManager.currentTab else { return }

        // 提供触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.prepare() // 预准备以减少延迟
        impactFeedback.impactOccurred()

        // 使用优化的动画切换 Tab
        withAnimation(DesignSystemConstants.standardEaseAnimation) {
            contentManager.currentTab = tab
        }

        // 如果弹窗是收起状态，自动展开以显示内容
        if contentManager.bottomSheetState == .collapsed {
            // 延迟展开，让 Tab 切换动画先完成
            DispatchQueue.main.asyncAfter(deadline: .now() + NewItineraryConstants.Timing.uiUpdateDelay) {
                contentManager.updateBottomSheetState(.full, animated: true)
            }
        }
    }

    private var tabContentView: some View {
        TabView(selection: $contentManager.currentTab) {
            CreateItineraryContentView()
                .environmentObject(contentManager)
                .tag(TabModel.create)

            AIPlanningContentView()
                .environmentObject(contentManager)
                .tag(TabModel.smart)

            ImportGuideContentView()
                .environmentObject(contentManager)
                .tag(TabModel.importGuide)
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
    }

}

#Preview {
    ZStack {
        LinearGradient(
            colors: [
                Color(.systemBlue).opacity(0.1),
                Color(.systemTeal).opacity(0.05)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()

        NewItineraryContentView(contentManager: ContentManager())
    }
}