import SwiftUI

/// 底部弹窗视图
struct BottomSheetView<Content: View>: View {
    private let content: Content
    @ObservedObject private var contentManager: ContentManager

    // MARK: - 环境变量
    @Environment(\.colorScheme) private var colorScheme

    init(contentManager: ContentManager, @ViewBuilder content: () -> Content) {
        self.contentManager = contentManager
        self.content = content()
    }

    // MARK: - 颜色计算属性

    /// 底部弹窗背景色 - 深色模式优化
    private var sheetBackgroundColor: Color {
        switch colorScheme {
        case .dark:
            // 深色模式下使用稍亮的灰色，与卡片的纯黑色形成层次
            return Color(.systemGray6)
        case .light:
            // 浅色模式保持原有的分组背景色
            return Color(.systemGroupedBackground)
        @unknown default:
            return Color(.systemGroupedBackground)
        }
    }

    var body: some View {
        GeometryReader { geometry in
            VStack {
                Spacer()

                sheetContent
                    .offset(y: currentOffset)
                    .gesture(dragGesture)
                    .animation(
                        contentManager.isDragging ? nil : DesignSystemConstants.standardSpringAnimation,
                        value: contentManager.bottomSheetState
                    )
            }
            .onAppear {
                contentManager.updateGeometry(geometry)
            }
        }
        .ignoresSafeArea(.container, edges: .bottom)
    }

    private var sheetContent: some View {
        VStack(spacing: 0) {
            dragIndicatorArea
            content
            Spacer(minLength: 0)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .background(sheetBackgroundColor)
        .topCornerRadius(DesignSystemConstants.CornerRadius.large)
    }

    // MARK: - 计算属性

    /// 当前偏移量 - 直接使用 ContentManager 计算的偏移量
    private var currentOffset: CGFloat {
        return contentManager.offsetY
    }

    // MARK: - 子视图

    private var dragIndicatorArea: some View {
        VStack(spacing: 0) {
            Rectangle()
                .fill(Color.clear)
                .frame(height: DesignSystemConstants.Spacing.small + 4) // 12pt

            Capsule()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 40, height: 5)

            Rectangle()
                .fill(Color.clear)
                .frame(height: DesignSystemConstants.Spacing.small + 4) // 12pt
        }
        .contentShape(Rectangle())
        .gesture(dragGesture)
    }
    
    private var dragGesture: some Gesture {
        DragGesture(minimumDistance: 1, coordinateSpace: .global)
            .onChanged { value in
                if !contentManager.isDragging {
                    contentManager.handleDragStart()
                }
                contentManager.handleDragChange(value.translation.height)
            }
            .onEnded { value in
                contentManager.handleDragEnd(velocity: value.velocity.height)
            }
    }


}

#Preview {
    ZStack {
        Color.blue.ignoresSafeArea()
        BottomSheetView(contentManager: ContentManager()) {
            VStack {
                Text("弹窗内容").font(.title).padding()
                ScrollView {
                    LazyVStack {
                        ForEach(0..<20, id: \.self) { index in
                            Text("项目 \(index)")
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.gray.opacity(0.1))
                        }
                    }
                }
            }
        }
    }
}
