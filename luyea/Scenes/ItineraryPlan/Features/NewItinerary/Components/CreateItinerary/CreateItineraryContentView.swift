import Foundation
import SwiftUI

/// 创建行程Tab内容视图
///
/// 手动创建行程的主要界面，包含目的地选择、景点搜索、时间规划等功能。
/// 采用MVVM架构，通过ViewModel管理状态和业务逻辑。
struct CreateItineraryContentView: View {

    // MARK: - Dependencies

    /// 内容管理器，负责UI状态和交互管理
    @EnvironmentObject var contentManager: ContentManager

    // MARK: - ViewModel

    @StateObject private var viewModel = ItineraryCreationViewModel()

    // MARK: - Local UI State

    @State private var isInitialized = false

    // MARK: - Toast已移至全局管理
    // 注意：所有Toast现在使用ToastManager.shared全局实例

    // MARK: - Constants

    private enum Constants {
        static let sectionSpacing: CGFloat = 24
        static let contentPadding: CGFloat = 8   // 内容区域紧凑边距
        static let bottomSpacing: CGFloat = 120
        static let animationDuration: TimeInterval = DesignSystemConstants.Animation.standard
    }

    // MARK: - Body

    var body: some View {
        GeometryReader { geometry in
            ScrollViewReader { scrollProxy in
                ScrollView(.vertical, showsIndicators: false) {
                    LazyVStack(spacing: Constants.sectionSpacing) {
                        // 目的地选择区域
                        destinationSelectionSection
                        
                        // 景点搜索区域
                        attractionSearchSection(scrollProxy: scrollProxy)
                        
                        // 已选择景点区域
                        if viewModel.hasSelectedAttractions {
                            selectedAttractionsSection
                        }
                        
                        // 城市推荐区域
                        if viewModel.showRecommendationSection {
                            cityRecommendationSection
                        }
                        
                        // 行程计划区域
                        if viewModel.showRecommendationSection {
                            tripPlanningSection
                        }
                        
                        // 创建按钮
                        if viewModel.showRecommendationSection {
                            createItineraryButton
                        }
                        
                        // 底部填充空间
                        bottomSpacing(geometry: geometry)
                    }
                }
                .onAppear {
                    handleViewAppear()
                }
                .onChange(of: viewModel.selectedDestinations) { _, _ in
                    handleDestinationChange()
                }
                .onChange(of: viewModel.selectedAttractions) { _, _ in
                    handleAttractionChange(scrollProxy: scrollProxy)
                }
                .sheet(isPresented: $viewModel.showDestinationPicker) {
                    destinationPickerSheet
                }
                .sheet(isPresented: $viewModel.showFavoritesList) {
                    favoritesListSheet(scrollProxy: scrollProxy)
                }
            }
        }
    }
    
    // MARK: - Private Views
    
    /// 目的地选择区域
    private var destinationSelectionSection: some View {
        DestinationSelectionSection(
            selectedDestinations: $viewModel.selectedDestinations,
            showDestinationPicker: $viewModel.showDestinationPicker,
            onDestinationRemoved: { destination in
                viewModel.removeDestination(destination)
                notifyCoordinator()
            }
        )
    }
    
    /// 景点搜索区域
    private func attractionSearchSection(scrollProxy: ScrollViewProxy) -> some View {
        AttractionSearchSection(
            selectedAttractions: $viewModel.selectedAttractions,
            favoriteAttractions: $viewModel.favoriteAttractions,
            showFavoritesList: $viewModel.showFavoritesList,
            onAttractionToggled: { attraction in
                viewModel.toggleAttractionSelection(attraction)
                notifyCoordinator()
            },
            onAttractionAddedFromSearch: { attraction in
                handleAttractionAdded(scrollProxy: scrollProxy)
            }
        )
    }
    
    /// 已选择景点区域
    private var selectedAttractionsSection: some View {
        SelectedAttractionsSection(
            selectedAttractions: $viewModel.selectedAttractions,
            onAttractionRemoved: { attraction in
                viewModel.removeAttraction(attraction)
                notifyCoordinator()
            }
        )
        .id("selectedAttractionsAnchor")
        .transition(.asymmetric(
            insertion: .move(edge: .top).combined(with: .opacity),
            removal: .move(edge: .top).combined(with: .opacity)
        ))
    }
    
    /// 城市推荐区域
    private var cityRecommendationSection: some View {
        CityRecommendationSection(
            enableCityRecommendation: $viewModel.enableCityRecommendation,
            recommendationTypes: $viewModel.recommendationTypes,
            selectedDestinations: viewModel.selectedDestinations,
            selectedAttractions: viewModel.selectedAttractions,
            shouldForceEnable: viewModel.shouldForceEnableRecommendation
        )
    }
    
    /// 行程计划区域
    private var tripPlanningSection: some View {
        TripPlanningSection(
            departureLocation: $viewModel.departureLocation,
            departureDate: $viewModel.departureDate,
            hasSelectedDate: $viewModel.hasSelectedDate,
            tripDays: $viewModel.tripDays,
            includeReturn: $viewModel.includeReturn,
            estimatedBudget: $viewModel.estimatedBudget
        )
    }
    
    /// 创建按钮
    private var createItineraryButton: some View {
        CreateItineraryButtonView(
            selectedDestinations: viewModel.selectedDestinations,
            selectedAttractions: viewModel.selectedAttractions,
            departureLocation: viewModel.departureLocation,
            departureDate: viewModel.hasSelectedDate ? viewModel.departureDate : nil,
            tripDays: viewModel.tripDays,
            includeReturn: viewModel.includeReturn,
            estimatedBudget: viewModel.estimatedBudget,
            enableCityRecommendation: viewModel.enableCityRecommendation,
            recommendationTypes: viewModel.recommendationTypes,
            isCreating: viewModel.isCreatingItinerary,
            onCreateItinerary: {
                handleCreateItinerary()
            }
        )
    }
    
    /// 底部填充空间
    private func bottomSpacing(geometry: GeometryProxy) -> some View {
        Spacer(minLength: max(
            Constants.bottomSpacing,
            geometry.safeAreaInsets.bottom + 80
        ))
    }
    
    /// 目的地选择器弹窗
    private var destinationPickerSheet: some View {
        MultiDestinationPickerSheet(
            selectedDestinations: $viewModel.selectedDestinations,
            isPresented: $viewModel.showDestinationPicker,
            onDestinationsConfirmed: { destinations in
                // 更新选中的目的地
                viewModel.selectedDestinations = destinations

                // 如果有目的地选择，自动展开底部弹窗
                // 直接检查是否有选中的目的地，而不依赖showRecommendationSection的异步更新
                if !destinations.isEmpty || !viewModel.selectedAttractions.isEmpty {
                    contentManager.updateBottomSheetState(.full, animated: true)
                    Log.debug("📱 目的地选择完成，展开底部弹窗")
                }

                notifyCoordinator()
            }
        )
    }
    
    /// 收藏列表弹窗
    private func favoritesListSheet(scrollProxy: ScrollViewProxy) -> some View {
        FavoriteAttractionsSheet(
            favoriteAttractions: $viewModel.favoriteAttractions,
            selectedAttractions: $viewModel.selectedAttractions,
            isPresented: $viewModel.showFavoritesList,
            onAttractionsConfirmed: { attractions in
                // 记录添加前的状态
                let wasEmpty = viewModel.selectedAttractions.isEmpty

                // 🔧 修复：使用 addAttraction 方法逐个添加，确保触发城市自动添加逻辑
                for attraction in attractions {
                    viewModel.addAttraction(attraction)
                }

                // 智能判断是否需要滚动和展开
                handleFavoriteAttractionsConfirmed(
                    attractions: attractions,
                    wasEmpty: wasEmpty,
                    scrollProxy: scrollProxy
                )

                notifyCoordinator()
            }
        )
    }
    
    // MARK: - Private Methods
    
    /// 处理视图出现
    private func handleViewAppear() {
        guard !isInitialized else { return }

        isInitialized = true
        Log.debug("📱 创建行程视图出现")
    }
    
    /// 处理目的地变化
    private func handleDestinationChange() {
        withAnimation(.easeInOut(duration: Constants.animationDuration)) {
            // 目的地变化时的UI更新逻辑
        }

        // 如果有目的地或景点，自动展开底部弹窗
        if !viewModel.selectedDestinations.isEmpty || !viewModel.selectedAttractions.isEmpty {
            contentManager.updateBottomSheetState(.full, animated: true)
            Log.debug("📱 目的地变化，展开底部弹窗")
        }

        notifyCoordinator()
    }
    
    /// 处理景点变化
    private func handleAttractionChange(scrollProxy: ScrollViewProxy) {
        withAnimation(.easeInOut(duration: Constants.animationDuration)) {
            // 景点变化时的UI更新逻辑
        }

        // 如果有目的地或景点，自动展开底部弹窗
        if !viewModel.selectedDestinations.isEmpty || !viewModel.selectedAttractions.isEmpty {
            contentManager.updateBottomSheetState(.full, animated: true)
            Log.debug("📱 景点变化，展开底部弹窗")
        }

        notifyCoordinator()
    }
    
    /// 处理景点添加
    private func handleAttractionAdded(scrollProxy: ScrollViewProxy) {
        if viewModel.selectedAttractions.count == 1 {
            // 使用Task替代硬编码延迟
            Task { @MainActor in
                try? await Task.sleep(for: .milliseconds(Int(DesignSystemConstants.Interaction.debounceDelay * 1000)))
                withAnimation(DesignSystemConstants.slowEaseAnimation) {
                    scrollProxy.scrollTo("selectedAttractionsAnchor", anchor: .center)
                }
            }
        }
    }

    /// 处理收藏地点确认选择
    private func handleFavoriteAttractionsConfirmed(
        attractions: [AttractionModel],
        wasEmpty: Bool,
        scrollProxy: ScrollViewProxy
    ) {
        Log.debug("📱 收藏地点确认处理 - 添加前为空: \(wasEmpty), 添加后数量: \(attractions.count)")

        // 如果有景点选择，自动展开底部弹窗
        if !attractions.isEmpty || !viewModel.selectedDestinations.isEmpty {
            contentManager.updateBottomSheetState(.full, animated: true)
            Log.debug("📱 收藏地点选择完成，展开底部弹窗")
        }

        // 使用与主界面完全一致的滚动逻辑：只要是首次添加就滚动
        if wasEmpty && !attractions.isEmpty {
            Log.debug("📱 首次添加收藏地点，执行滚动")

            // 使用Task替代硬编码延迟
            Task { @MainActor in
                try? await Task.sleep(for: .milliseconds(Int(DesignSystemConstants.Interaction.debounceDelay * 1000)))
                withAnimation(DesignSystemConstants.slowEaseAnimation) {
                    scrollProxy.scrollTo("selectedAttractionsAnchor", anchor: .center)
                }
            }
            Log.debug("📱 首次添加收藏地点，自动滚动到已添加区域")
        } else {
            Log.debug("📱 不满足滚动条件 - 添加前为空: \(wasEmpty), 添加后不为空: \(!attractions.isEmpty)")
        }
    }
    
    /// 处理创建行程
    private func handleCreateItinerary() {
        viewModel.createItinerary()

        // 使用全局Toast管理器
        ToastManager.shared.show("行程创建成功", duration: 2.5)

        Log.debug("📱 行程创建完成")
    }
    

    /// 通知协调器
    private func notifyCoordinator() {
        // 这里可以添加与协调器的通信逻辑
        // 例如：更新地图标注、同步状态等

        // 未来可以扩展为：
        // coordinator.syncDataFromCreateTab(
        //     destinations: viewModel.selectedDestinations,
        //     attractions: viewModel.selectedAttractions
        // )
    }
}

// MARK: - Preview

#Preview("创建行程") {
    CreateItineraryContentView()
        .environmentObject(ContentManager())
        .padding()
        .background(Color(.systemGroupedBackground))
}
