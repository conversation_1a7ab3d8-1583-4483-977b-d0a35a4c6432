import SwiftUI

/// 多选目的地选择弹窗组件
///
/// 功能特性：
/// - 支持多选目的地
/// - 现代化弹窗设计
/// - 目的地搜索和选择
/// - 已选择目的地管理
///
/// 重构说明：
/// - 拆分为独立的子组件，提高可维护性
/// - 优化数据加载和搜索性能
/// - 简化视图层次结构
struct MultiDestinationPickerSheet: View {
    
    // MARK: - Properties

    @Binding var selectedDestinations: [Destination]
    @Binding var isPresented: Bool
    let onDestinationsConfirmed: (([Destination]) -> Void)?
    
    // MARK: - State

    @State private var searchText: String = ""
    @State private var destinations: [Destination] = []
    @State private var isLoading: Bool = false
    @State private var temporarySelectedDestinations: [Destination] = []
    // 注意：Toast已移至全局管理，使用ToastManager.shared
    @StateObject private var searchDebouncer = Debouncer(delay: DesignSystemConstants.Interaction.debounceDelay)

    // MARK: - Static Cache

    /// 静态缓存，避免重复加载
    private static var cachedDestinations: [Destination]?
    private static var hasLoadedOnce = false

    // MARK: - Constants

    private let maxDestinationCount = 10

    // MARK: - Computed Properties

    /// 临时选择目的地的ID集合（用于快速查找）
    private var temporarySelectedDestinationIds: Set<String> {
        Set(temporarySelectedDestinations.map { $0.id })
    }

    /// 过滤后的目的地列表（高性能优化）
    private var filteredDestinations: [Destination] {
        // 缓存城市类型的目的地，避免重复过滤
        let cityDestinations = destinations.lazy.filter { $0.type == .city }

        if searchText.isEmpty {
            return Array(cityDestinations)
        } else {
            // 使用更高效的搜索算法
            let lowercaseSearchText = searchText.lowercased()
            return cityDestinations.compactMap { destination in
                destination.name.lowercased().contains(lowercaseSearchText) ? destination : nil
            }
        }
    }

    // MARK: - Body
    
    var body: some View {
        NavigationStack {
            ZStack {
                VStack(spacing: 0) {
                    // 临时选择目的地展示（始终显示）
                    temporarySelectedDestinationsHeader

                    // 搜索栏
                    searchSection

                    // 目的地列表
                    destinationList
                }
                .background(Color(.systemGroupedBackground))


            }
            .navigationTitle("选择目的地")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        isPresented = false
                    }
                    .foregroundColor(.blue)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确认(\(temporarySelectedDestinations.count))") {
                        confirmSelection()
                    }
                    .foregroundColor(temporarySelectedDestinations.isEmpty ? .gray : .blue)
                    .fontWeight(.semibold)
                    .disabled(temporarySelectedDestinations.isEmpty)
                }
            }
        }
        // 全局Toast无需添加修饰符
        .onAppear {
            loadDestinations()
            // 初始化临时选择状态
            temporarySelectedDestinations = selectedDestinations
        }
    }
    
    // MARK: - Private Views

    /// 临时选择目的地头部
    private var temporarySelectedDestinationsHeader: some View {
        HStack(spacing: 1) {
            // 标题和城市名称在同一行
            HStack(spacing: 0) {
                // 根据选择状态显示不同的标题
                if temporarySelectedDestinations.isEmpty {
                    Text("未选目的地")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                } else {
                    Text("已选：")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)

                    // 横向滚动的城市名称列表
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(temporarySelectedDestinations) { destination in
                                SelectedDestinationChip(destination: destination) {
                                    removeFromTemporarySelection(destination)
                                }
                            }
                        }
                    }
                }
            }

            Spacer()

            // 清空按钮区域，参考话题选择栏的清空按钮样式
            if !temporarySelectedDestinations.isEmpty {
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        temporarySelectedDestinations.removeAll()
                    }
                }) {
                    HStack(spacing: 2) {
                        Image(systemName: "trash")
                            .font(.system(size: 11))
                            .foregroundColor(.red.opacity(0.8))
                        Text("清空")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(.red.opacity(0.8))
                    }
                    .padding(.horizontal, 4)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.red.opacity(0.08))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(Color.red.opacity(0.2), lineWidth: 0.5)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .frame(height: 26) // 参考话题选择栏的高度
        .padding(.horizontal)
        .background(Color(.systemBackground))
        .shadow(color: Color.primary.opacity(0.05), radius: 2, x: 0, y: 2)
    }
    
    /// 搜索区域
    private var searchSection: some View {
        VStack(spacing: 0) {
            HStack(spacing: 16) {
                Image(systemName: "magnifyingglass")
                    .font(.title3)
                    .foregroundColor(.gray)
                
                TextField("搜索城市名称", text: $searchText)
                    .font(.body)
                    .onChange(of: searchText) { _, newValue in
                        // 使用统一的防抖延迟
                        handleSearchTextChange(newValue)
                    }
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title3)
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding()
            .background(Color(.systemBackground))
            
            Divider()
        }
    }
    
    /// 目的地列表 - 性能优化版本
    private var destinationList: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(filteredDestinations, id: \.id) { destination in
                    OptimizedDestinationRow(
                        destination: destination,
                        isSelected: temporarySelectedDestinationIds.contains(destination.id),
                        onTap: {
                            // 使用异步操作，确保点击响应不被阻塞
                            Task { @MainActor in
                                toggleTemporarySelection(destination)
                            }
                        }
                    )
                    .id(destination.id)
                }
            }
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: ItineraryPlanConstants.Layout.cardCornerRadius))
            .padding(.horizontal)
            .padding(.top)
        }
        .onAppear {
            // 大幅延迟预加载可见区域的图片，确保弹窗动画完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                self.preloadVisibleImages()
            }
        }
    }
    
    // MARK: - Computed Properties (已在上方定义)
    
    // MARK: - Private Methods
    
    /// 切换临时选择状态（超高性能优化版本 - 确保UI响应性）
    private func toggleTemporarySelection(_ destination: Destination) {
        // 预先检查是否已选择，避免重复计算
        let isCurrentlySelected = temporarySelectedDestinationIds.contains(destination.id)

        // 立即提供触觉反馈，不等待动画
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        if isCurrentlySelected {
            // 移除选择 - 使用更快的动画
            withAnimation(.easeInOut(duration: 0.1)) {
                temporarySelectedDestinations.removeAll { $0.id == destination.id }
            }
        } else {
            // 添加选择 - 先检查数量限制
            guard temporarySelectedDestinations.count < maxDestinationCount else {
                // 显示全局toast提示
                ToastManager.shared.show("最多只能选择\(maxDestinationCount)个目的地", duration: 3.0)

                // 使用警告触觉反馈（异步执行，不阻塞UI）
                Task.detached(priority: .background) {
                    await MainActor.run {
                        let notificationFeedback = UINotificationFeedbackGenerator()
                        notificationFeedback.notificationOccurred(.warning)
                    }
                }

                return
            }

            // 添加到选择列表 - 使用更快的动画
            withAnimation(.easeInOut(duration: 0.1)) {
                temporarySelectedDestinations.insert(destination, at: 0)
            }
        }
    }

    /// 处理搜索文本变化 - 使用统一的防抖机制
    private func handleSearchTextChange(_ newValue: String) {
        // 使用新的通用防抖工具
        searchDebouncer.call {
            // 这里可以添加搜索逻辑，目前使用计算属性已足够
        }
    }

    /// 从临时选择中移除目的地
    private func removeFromTemporarySelection(_ destination: Destination) {
        withAnimation(.easeInOut(duration: 0.2)) {
            temporarySelectedDestinations.removeAll { $0.id == destination.id }
        }
    }

    /// 确认选择的目的地
    private func confirmSelection() {
        if !temporarySelectedDestinations.isEmpty {
            onDestinationsConfirmed?(temporarySelectedDestinations)
            // 关闭弹窗
            isPresented = false
        }
    }

    // 注意：Toast功能已迁移到LocalToastManager

    /// 预加载前几张图片，提升用户体验（大幅延迟执行，确保UI完全稳定）
    private func preloadInitialImages() {
        // 获取前3个城市的图片URL进行预加载（进一步减少数量）
        let imageUrls = self.destinations.prefix(3).compactMap { $0.imageUrl }

        if !imageUrls.isEmpty {
            Log.debug("🖼️ 预加载 \(imageUrls.count) 张城市图片")
            // 使用Task包装actor调用，避免Swift 6警告
            Task {
                await ImagePreloadService.shared.preloadImages(urls: imageUrls, priority: .background)
            }
        }
    }

    /// 预加载可见区域的图片
    private func preloadVisibleImages() {
        // 预加载前8张图片，减少数量避免卡顿
        let imageUrls = filteredDestinations.prefix(8).compactMap { $0.imageUrl }

        if !imageUrls.isEmpty {
            Log.debug("👁️ 预加载可见区域 \(imageUrls.count) 张图片")
            // 使用Task包装actor调用，避免Swift 6警告
            Task {
                await ImagePreloadService.shared.preloadImages(urls: imageUrls, priority: .userInitiated)
            }
        }
    }

    /// 智能预加载即将出现的图片（低优先级，不阻塞滚动）
    private func preloadUpcomingImages(currentIndex: Int) {
        // 当滚动到某个位置时，预加载后续2张图片（进一步减少数量）
        let startIndex = currentIndex + 1
        let endIndex = min(startIndex + 2, filteredDestinations.count)

        if startIndex < filteredDestinations.count {
            let upcomingDestinations = Array(filteredDestinations[startIndex..<endIndex])
            let imageUrls = upcomingDestinations.compactMap { $0.imageUrl }

            if !imageUrls.isEmpty {
                // 使用Task包装actor调用，避免Swift 6警告
                Task {
                    await ImagePreloadService.shared.preloadImages(urls: imageUrls, priority: .background)
                }
            }
        }
    }
    
    /// 加载目的地数据（高性能优化版本 - 使用缓存避免重复加载）
    private func loadDestinations() {
        // 如果有缓存且已加载过，直接使用缓存
        if let cached = Self.cachedDestinations, Self.hasLoadedOnce {
            destinations = cached
            isLoading = false

            // 延迟预加载，确保UI完全渲染完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.preloadInitialImages()
            }
            return
        }

        isLoading = true

        // 使用后台队列处理数据，完全不阻塞主线程
        Task.detached(priority: .userInitiated) {
            // 获取去重后的城市数据
            let allCities = UnifiedDestinationService.shared.getAllAvailableCities()

            // 进一步确保ID唯一性
            var uniqueCities: [Destination] = []
            var seenIds: Set<String> = []

            for city in allCities {
                if !seenIds.contains(city.id) {
                    seenIds.insert(city.id)
                    uniqueCities.append(city)
                }
            }

            // ID唯一性已通过上面的 seenIds 集合确保

            // 回到主线程更新UI
            await MainActor.run { [destinations = uniqueCities] in
                self.destinations = destinations
                self.isLoading = false

                // 缓存数据
                Self.cachedDestinations = destinations
                Self.hasLoadedOnce = true

                // 大幅延迟预加载，确保弹窗动画完全完成
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    self.preloadInitialImages()
                }
            }
        }
    }



    /// 从服务加载目的地数据（可能抛出错误）
    private func loadDestinationsFromServiceWithError() async throws -> [Destination] {
        // 模拟异步加载，可能失败
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒

        // 实际项目中这里应该调用真实的网络请求
        // let destinations = try await DestinationService.shared.fetchDestinations()

        // 模拟可能的网络错误（10% 概率）
        if Int.random(in: 1...10) == 1 {
            throw NetworkError.timeout
        }

        return Destination.samples + DestinationDataProvider.additionalCities
    }

    /// 从服务加载目的地数据（不抛出错误的版本）
    private func loadDestinationsFromService() async -> [Destination] {
        do {
            return try await loadDestinationsFromServiceWithError()
        } catch {
            // 如果加载失败，返回默认数据
            Log.warning("⚠️ [MultiDestinationPicker] 加载目的地数据失败，使用默认数据: \(error)")
            return Destination.samples
        }
    }
}

// MARK: - Preview

#Preview {
    MultiDestinationPickerSheet(
        selectedDestinations: .constant([]),
        isPresented: .constant(true),
        onDestinationsConfirmed: { destinations in
            print("确认添加目的地: \(destinations.map { $0.name }.joined(separator: ", "))")
        }
    )
}



/// 优化的目的地行组件（高性能版本）
private struct OptimizedDestinationRow: View {
    let destination: Destination
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 城市图片 - 使用缓存的异步加载
                CachedAsyncImage(
                    url: URL(string: destination.imageUrl ?? "")
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                                .font(.system(size: 16))
                        )
                } errorView: {
                    // 使用自动尺寸的通用图片错误视图组件
                    ImageErrorView()
                }
                .frame(width: 50, height: 50)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
                )

                // 城市信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(destination.name)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    if let description = destination.description {
                        Text(description)
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }

                Spacer()

                // 选择状态指示器
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(isSelected ? .blue : .gray.opacity(0.5))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .overlay(
                Rectangle()
                    .frame(height: 0.5)
                    .foregroundColor(.gray.opacity(0.3)),
                alignment: .bottom
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 注意：Toast功能已统一使用ToastManager.shared
