import Foundation
import SwiftUI

/// 导入攻略Tab内容视图
///
/// 导入现有攻略模板的主界面，支持通过URL或文本导入攻略内容。
///
/// 功能特性：
/// - 攻略URL和文本输入
/// - 输入验证和状态管理
/// - 响应式表单设计
struct ImportGuideContentView: View {

    // MARK: - Dependencies

    /// 内容管理器，负责UI状态管理
    @EnvironmentObject var contentManager: ContentManager

    // MARK: - State

    @State private var importURL: String = ""

    // MARK: - Body

    var body: some View {
        VStack(spacing: 32) {
            headerSection
            inputSection
            actionSection

            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 24)
        .onAppear {
            handleViewAppear()
        }
    }

    // MARK: - Private Views

    /// 头部区域
    private var headerSection: some View {
        VStack(spacing: 16) {
            iconView
        }
        .padding(.top, 8)
    }

    /// 图标视图
    private var iconView: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(Color.accentColor.opacity(0.1))
                .frame(width: 80, height: 80)

            // 图标
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(.accentColor)
        }
    }

    /// 输入区域
    private var inputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            inputLabel
            textEditorWithPlaceholder
            inputHint
        }
    }

    /// 输入框标签
    private var inputLabel: some View {
        Text("攻略信息")
            .font(.headline.weight(.medium))
            .foregroundColor(.primary)
    }

    /// 输入提示
    private var inputHint: some View {
        HStack(spacing: 6) {
            Image(systemName: "info.circle.fill")
                .font(.caption)
                .foregroundColor(.accentColor)

            Text("将其他平台的攻略分享链接输入，AI将为您智能解析行程信息")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    /// 带占位符的文本编辑器
    private var textEditorWithPlaceholder: some View {
        ZStack {
            // 背景和边框
            RoundedRectangle(cornerRadius: 16, style: .continuous)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16, style: .continuous)
                        .stroke(importURL.isEmpty ? Color(.systemGray4) : Color.blue, lineWidth: 1.5)
                )
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)

            // TextEditor 和占位符使用相同的容器
            textEditorContainer

            if !importURL.isEmpty {
                // 清空按钮 - 右下角
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        clearButton
                    }
                }
                .padding(12)
            }
        }
        .frame(height: 120)
    }

    /// TextEditor 容器 - 使用相对定位确保完美对齐
    private var textEditorContainer: some View {
        ZStack {
            // 实际的 TextEditor
            textEditor

            // 占位符 - 使用相对定位
            if importURL.isEmpty {
                GeometryReader { geometry in
                    placeholderText
                        .offset(
                            x: 5, // TextEditor内部文本容器的左边距补偿
                            y: 8  // TextEditor内部文本容器的顶部边距补偿
                        )
                }
            }
        }
    }

    /// 清空按钮
    private var clearButton: some View {
        Button(action: handleClearAction) {
            Image(systemName: "xmark.circle.fill")
                .font(.title3)
                .foregroundColor(.secondary)
                .background(Color(.systemBackground), in: Circle())
        }
        .buttonStyle(.plain)
    }

    /// 文本编辑器
    private var textEditor: some View {
        TextEditor(text: $importURL)
            .scrollContentBackground(.hidden)
            .padding(EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16))
            .font(.body)
            .tint(.blue)
    }

    /// 占位符文本
    private var placeholderText: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("粘贴攻略内容分享链接...")
                .foregroundColor(Color(.placeholderText))
                .font(.body)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .frame(maxWidth: .infinity, alignment: .topLeading)
        .allowsHitTesting(false)
    }

    // MARK: - Private Methods

    /// 处理视图出现
    private func handleViewAppear() {
        // 视图出现时的处理逻辑
    }

    /// 操作区域
    private var actionSection: some View {
        analyzeButton
    }

    /// 分析按钮
    private var analyzeButton: some View {
        Button(action: handleStartAnalysis) {
            HStack(spacing: 10) {
                Image(systemName: "sparkles")
                    .font(.headline.weight(.semibold))

                Text("开始智能分析")
                    .font(.headline.weight(.semibold))
            }
            .foregroundColor(buttonContentColor)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(buttonBackgroundGradient)
            .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))
            .shadow(
                color: buttonShadowColor,
                radius: 8,
                x: 0,
                y: 4
            )
        }
        .disabled(importURL.isEmpty)
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(importURL.isEmpty ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: importURL.isEmpty)
    }

    /// 按钮内容颜色（文字和图标）
    private var buttonContentColor: Color {
        if importURL.isEmpty {
            return Color(.systemGray)  // 禁用状态使用更深的灰色，增强对比度
        } else {
            return .white  // 激活状态使用白色
        }
    }

    /// 按钮背景渐变
    private var buttonBackgroundGradient: LinearGradient {
        if importURL.isEmpty {
            return LinearGradient(
                colors: [Color(.systemGray4), Color(.systemGray3)],  // 使用更深的灰色背景，增强与页面背景的对比
                startPoint: .leading,
                endPoint: .trailing
            )
        } else {
            return LinearGradient(
                colors: [Color.blue, Color.blue.opacity(0.8)],
                startPoint: .leading,
                endPoint: .trailing
            )
        }
    }

    /// 按钮阴影颜色
    private var buttonShadowColor: Color {
        if importURL.isEmpty {
            return .clear
        } else {
            return Color.blue.opacity(0.3)
        }
    }

    // MARK: - Actions

    /// 处理清空操作
    private func handleClearAction() {
        importURL = ""
    }

    /// 处理开始分析
    private func handleStartAnalysis() {
        guard !importURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            ToastManager.shared.show("请输入攻略内容", style: .warning)
            return
        }

        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // 显示开发中提示
        ToastManager.shared.show("分析功能开发中，敬请期待", style: .info)
    }
}

#Preview {
    ImportGuideContentView()
        .environmentObject(ContentManager())
        .padding()
        .background(Color(.systemGroupedBackground))
}
