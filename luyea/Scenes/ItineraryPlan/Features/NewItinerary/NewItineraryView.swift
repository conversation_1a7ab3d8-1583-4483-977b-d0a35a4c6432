import Combine
import MapKit
import SwiftUI

/// 新建行程主入口视图
struct NewItineraryView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var mapBackgroundManager = MapBackgroundManager()
    @StateObject private var contentManager = ContentManager()
    @State private var loadingPhase: LoadingPhase = .contentOnly

    private var topLevelBackButton: some View {
        Button(action: {
            dismiss()
        }) {
            Image(systemName: "chevron.left")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 40, height: 40)
                .background(Color.black.opacity(0.3))
                .clipShape(Circle())
                .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
        }
        .padding(.leading, 16)
        .padding(.top, 8)
    }

    var body: some View {
        ZStack {
            Group {
                if loadingPhase.shouldCreateMap {
                    BackgroundMapLayer(mapBackgroundManager: mapBackgroundManager)
                        .opacity(loadingPhase.mapOpacity)
                        .animation(.easeInOut(duration: 0.5), value: loadingPhase)
                        .transition(.opacity)
                } else {
                    mapPlaceholderView
                }
            }
            .ignoresSafeArea(.all, edges: .all)

            NewItineraryContentView(contentManager: contentManager)
            .opacity(loadingPhase.contentOpacity)
            .animation(.easeOut(duration: 0.3), value: loadingPhase)
        }
        .overlay(alignment: .topLeading) {
            topLevelBackButton
        }
        .onAppear(perform: initializeView)
        .navigationBarBackButtonHidden(true)
        .navigationBarHidden(true)
        .enableSwipeBackGesture()
        .hideTabBar()
    }

    private var mapPlaceholderView: some View {
        ZStack {
            LinearGradient(
                colors: [
                    Color(.systemBlue).opacity(0.08),
                    Color(.systemTeal).opacity(0.04),
                    Color(.systemGray6)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            VStack(spacing: 8) {
                Image(systemName: "map")
                    .font(.system(size: 24, weight: .light))
                    .foregroundColor(.secondary.opacity(0.4))

                Text("准备地图...")
                    .font(.caption2)
                    .foregroundColor(.secondary.opacity(0.6))
            }
            .scaleEffect(0.9)
            .opacity(0.7)
        }
        .appBackground()
    }

    private func initializeView() {
        mapBackgroundManager.initialize()
        contentManager.initialize()
        Task { await loadUIPhases() }
    }

    private func loadUIPhases() async {
        try? await Task.sleep(for: .milliseconds(1200))

        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.5)) {
                loadingPhase = .mapLoading
            }
        }

        try? await Task.sleep(for: .milliseconds(800))

        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.8)) {
                loadingPhase = .fullyLoaded
            }
        }
    }
}

enum LoadingPhase {
    case contentOnly, mapLoading, fullyLoaded

    var mapOpacity: Double {
        switch self {
        case .contentOnly: return 0
        case .mapLoading: return 0.3
        case .fullyLoaded: return 1
        }
    }

    var contentOpacity: Double { 1 }

    var shouldCreateMap: Bool {
        switch self {
        case .contentOnly: return false
        case .mapLoading, .fullyLoaded: return true
        }
    }
}

#Preview {
    NavigationStack { NewItineraryView() }
}
