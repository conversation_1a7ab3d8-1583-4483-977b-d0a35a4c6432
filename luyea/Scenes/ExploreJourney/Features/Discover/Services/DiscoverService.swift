import Foundation
import Combine

/// 发现内容服务协议
protocol DiscoverServiceProtocol {
    /// 获取发现内容列表（轻量级数据）
    func fetchDiscoverItems(
        page: Int,
        pageSize: Int,
        topicIds: [String],
        destinationId: String?,
        destinationType: String?,
        showLikedOnly: Bool,
        showJourneyOnly: Bool
    ) async throws -> PaginationResponse<DiscoverListItem>


}

/// 发现内容服务实现
final class DiscoverService: DiscoverServiceProtocol {
    
    // MARK: - Dependencies
    
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 发现内容缓存（轻量级数据）
    private var discoverItemsCache: [String: PaginationResponse<DiscoverListItem>] = [:]


    
    /// 缓存时间戳
    private var cacheTimestamps: [String: Date] = [:]
    
    /// 缓存有效期（5分钟）
    private let cacheValidDuration: TimeInterval = 300
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取发现内容列表（轻量级数据）
    func fetchDiscoverItems(
        page: Int,
        pageSize: Int,
        topicIds: [String] = [],
        destinationId: String? = nil,
        destinationType: String? = nil,
        showLikedOnly: Bool = false,
        showJourneyOnly: Bool = false
    ) async throws -> PaginationResponse<DiscoverListItem> {
        
        // 构建缓存键
        let cacheKey = buildCacheKey(
            page: page,
            pageSize: pageSize,
            topicIds: topicIds,
            destinationId: destinationId,
            destinationType: destinationType,
            showLikedOnly: showLikedOnly,
            showJourneyOnly: showJourneyOnly
        )
        
        // 检查缓存
        if let cachedResponse = getCachedResponse(for: cacheKey) {
            Log.debug("🎯 [DiscoverService] 使用缓存数据: \(cacheKey)")
            return cachedResponse
        }
        
        // 构建查询参数
        var query: [String: String] = [
            "page": "\(page)",
            "pageSize": "\(pageSize)"
        ]

        if !topicIds.isEmpty {
            query["topicIds"] = topicIds.joined(separator: ",")
            Log.debug("🏷️ [DiscoverService] 话题筛选参数: \(topicIds.joined(separator: ","))")
        }

        if let destId = destinationId, !destId.isEmpty {
            query["destinationId"] = destId
            Log.debug("📍 [DiscoverService] 目的地筛选参数: \(destId)")
        }

        if let destType = destinationType {
            query["destinationType"] = destType
            Log.debug("🏢 [DiscoverService] 目的地类型参数: \(destType)")
        }

        if showLikedOnly {
            query["showLikedOnly"] = "true"
            Log.debug("❤️ [DiscoverService] 仅显示点赞内容")
        }

        if showJourneyOnly {
            query["showJourneyOnly"] = "true"
            Log.debug("🗺️ [DiscoverService] 仅显示包含行程内容")
        }

        Log.debug("🌐 [DiscoverService] 完整查询参数: \(query)")
        
        // 发起网络请求获取轻量级数据
        let response: PaginationResponse<DiscoverListItem> = try await networkService.request(
            .get("/discover/list").query(query)
        )

        // 缓存响应
        cacheResponse(response, for: cacheKey)

        Log.success("✅ [DiscoverService] 发现内容加载成功: \(response.items.count) 项")
        return response
    }



    // MARK: - Private Methods
    
    /// 构建缓存键
    private func buildCacheKey(
        page: Int,
        pageSize: Int,
        topicIds: [String],
        destinationId: String?,
        destinationType: String?,
        showLikedOnly: Bool,
        showJourneyOnly: Bool
    ) -> String {
        var components = [
            "page:\(page)",
            "size:\(pageSize)",
            "topics:\(topicIds.sorted().joined(separator:","))"
        ]
        
        if let destId = destinationId {
            components.append("destId:\(destId)")
        }
        
        if let destType = destinationType {
            components.append("destType:\(destType)")
        }
        
        if showLikedOnly {
            components.append("liked:true")
        }

        if showJourneyOnly {
            components.append("journey:true")
        }

        return components.joined(separator:"|")
    }
    
    /// 获取缓存响应
    private func getCachedResponse(for key: String) -> PaginationResponse<DiscoverListItem>? {
        guard let timestamp = cacheTimestamps[key],
              Date().timeIntervalSince(timestamp) < cacheValidDuration,
              let cachedResponse = discoverItemsCache[key] else {
            return nil
        }
        
        return cachedResponse
    }
    
    /// 缓存响应
    private func cacheResponse(_ response: PaginationResponse<DiscoverListItem>, for key: String) {
        discoverItemsCache[key] = response
        cacheTimestamps[key] = Date()
    }
    
    /// 清除缓存
    private func clearCache() {
        discoverItemsCache.removeAll()
        cacheTimestamps.removeAll()
    }
}

// MARK: - Supporting Models
// Comment模型已在DiscoverItem.swift中定义，这里不需要重复定义
