import SwiftUI

/// 发现内容图片视图
struct DiscoverItemImageView: View {
    /// 图片URL字符串
    let url: String?

    /// 固定的占位符高度，避免随机值导致的布局抖动
    @State private var placeholderHeight: CGFloat = {
        // 使用URL的哈希值生成稳定的高度，确保同一URL总是相同高度
        let baseHeight: CGFloat = 160
        let variation: CGFloat = 60
        return baseHeight + (variation * 0.5) // 使用固定的中间值
    }()

    var body: some View {
        CachedAsyncImage(
            url: URL(string: url ?? "")
        ) { image in
            image
                .resizable()
                .scaledToFill()
                .frame(maxHeight: DiscoverConstants.Card.maxImageHeight)
                .clipped()
        } placeholder: {
            ZStack {
                Color.gray.opacity(0.08)
                ProgressView()
                    .scaleEffect(0.8)
                    .tint(.gray)
            }
            .frame(height: placeholderHeight)
        } errorView: {
            // 使用自定义蓝色主题的错误视图
            ImageErrorView(
                style: .custom(
                    backgroundColor: Color.blue.opacity(0.06),
                    iconColor: Color.blue.opacity(0.5)
                ),
                showText: true,
                errorText: "图片加载失败",
                iconName: "photo"
            )
            .frame(height: 160)
        }
        .cornerRadius(5, corners: [UIRectCorner.topLeft, UIRectCorner.topRight]) // 发现卡片圆角
        .onAppear {
            // 只在首次出现时计算高度，基于URL生成稳定的随机高度
            if let urlString = url, !urlString.isEmpty {
                let hash = abs(urlString.hashValue)
                let normalizedHash = CGFloat(hash % 200) // 0-199的范围
                placeholderHeight = 80 + normalizedHash // 80-279的范围
            }
        }
    }
}

// MARK: - 位置标签组件

/// 发现内容位置标签视图
struct DiscoverItemLocationView: View {
    let location: String

    private let maxCharacters = 20
    private var displayLocation: String {
        if location.count > maxCharacters {
            return String(location.prefix(maxCharacters)) + "..."
        }
        return location
    }

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "mappin.and.ellipse")
                .font(.system(size: 12))
                .foregroundColor(.white)
                .accessibilityHidden(true)

            Text(displayLocation)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white)
                .lineLimit(1)
                .accessibilityLabel("位置: \(location)")
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(
            Color.black.opacity(0.5)
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 10))
        )
        .cornerRadius(10)
    }
}

// MARK: - 话题标签组件

/// 发现内容话题标签视图
struct DiscoverItemTagView: View {
    let topic: Topic
    let onTap: (Topic) -> Void
    let isInteractive: Bool

    init(topic: Topic, isInteractive: Bool = true, onTap: @escaping (Topic) -> Void) {
        self.topic = topic
        self.isInteractive = isInteractive
        self.onTap = onTap
    }

    var body: some View {
        Group {
            if isInteractive {
                Button(action: { onTap(topic) }) {
                    tagContent
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                tagContent
            }
        }
    }


    private var tagContent: some View {
        Text("#\(topic.name)")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.white)
            .padding(.vertical, 3)
            .padding(.horizontal, 6)
            .background(
                Color.black.opacity(0.5)
                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 4))
            )
            .cornerRadius(4)
            .accessibilityLabel("话题: \(topic.name)")
            .accessibilityHint(isInteractive ? "双击查看相关内容" : "")
    }
}

// MARK: - 主视图

/// 发现内容卡片视图
struct DiscoverItemView: View, Equatable {
    let item: DiscoverListItem
    let onTagPressed: ((Topic) -> Void)?
    let onCardTapped: (() -> Void)?
    let showTopicTag: Bool
    let isInteractive: Bool

    init(
        item: DiscoverListItem,
        showTopicTag: Bool = true,
        isInteractive: Bool = true,
        onTagPressed: ((Topic) -> Void)? = nil,
        onCardTapped: (() -> Void)? = nil
    ) {
        self.item = item
        self.showTopicTag = showTopicTag
        self.isInteractive = isInteractive
        self.onTagPressed = onTagPressed
        self.onCardTapped = onCardTapped
    }

    static func == (lhs: DiscoverItemView, rhs: DiscoverItemView) -> Bool {
        lhs.item == rhs.item
    }

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 图片区域
            imageSection

            // 内容区域
            contentSection
        }
        .background(Color(.systemBackground))
        .cornerRadius(5) // 发现卡片圆角
        .shadow(
            color: Color.primary.opacity(0.07), // 发现卡片阴影透明度
            radius: 6, // 发现卡片阴影半径
            x: 0,
            y: 2
        )
        .compositingGroup() // 将整个卡片组合为单个图层，提升渲染性能
        .frame(maxWidth: .infinity) // 限制卡片最大宽度
        .fixedSize(horizontal: false, vertical: true) // 允许垂直自适应，但限制水平扩展
        .contentShape(Rectangle())
        .onTapGesture {
            if isInteractive {
                onCardTapped?()
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityDescription)
    }

    // MARK: - View Sections

    /// 图片区域
    private var imageSection: some View {
        ZStack(alignment: .bottomLeading) {
            // 主图片
            DiscoverItemImageView(url: item.imageUrls.first)

            // 覆盖层内容
            overlayContent
        }
        .padding(.bottom, 8)
    }

    /// 覆盖层内容（位置和话题标签）
    private var overlayContent: some View {
        VStack(alignment: .leading, spacing: 8) {
            Spacer()

            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    // 话题标签
                    if showTopicTag, let topic = item.topic, let onTagPressed = onTagPressed {
                        DiscoverItemTagView(
                            topic: topic,
                            isInteractive: isInteractive,
                            onTap: onTagPressed
                        )
                    }

                    // 位置标签
                    if let location = item.location, !location.isEmpty {
                        DiscoverItemLocationView(location: location)
                    }
                }

                Spacer()
            }
        }
        .padding(8)
    }

    /// 内容区域
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题
            Text(item.title)
                .font(.system(size: 15, weight: .bold))
                .foregroundColor(.primary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
                .padding(.bottom, 2)

            // 描述
            Text(item.content)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .padding(.horizontal, 12)
        .padding(.bottom, 12)
    }



    // MARK: - Computed Properties

    /// 无障碍描述
    private var accessibilityDescription: String {
        var description = "\(item.title). \(item.content)"

        if let location = item.location, !location.isEmpty {
            description += ". 位置: \(location)"
        }

        if let topic = item.topic {
            description += ". 话题: \(topic.name)"
        }

        return description
    }
}

// MARK: - Preview

#Preview("发现内容卡片") {
    VStack(spacing: 16) {
        // 标准卡片
        DiscoverItemView(
            item: DiscoverListItem(
                id: "1",
                imageUrls: ["https://example.com/image.jpg"],
                title: "探索神秘的古城小巷",
                content: "漫步在古城的石板路上，感受历史的厚重与现代的活力交融。",
                likeCount: 128,
                location: "中国·江苏省苏州市",
                topic: Topic(id: "1", name: "古城探索", order: 1),
                allowFork: false
            ),
            onTagPressed: { topic in
                print("点击话题: \(topic.name)")
            },
            onCardTapped: {
                print("点击卡片")
            }
        )

        // 无话题标签的卡片
        DiscoverItemView(
            item: DiscoverListItem(
                id: "2",
                imageUrls: ["https://example.com/image2.jpg"],
                title: "山间云海日出",
                content: "清晨五点，站在山顶等待日出的那一刻，云海翻腾，金光万丈。",
                likeCount: 256,
                location: "中国·安徽省黄山市",
                topic: nil,
                allowFork: true
            ),
            showTopicTag: false
        )
    }
    .padding()
    .background(Color(.systemGroupedBackground))

}
