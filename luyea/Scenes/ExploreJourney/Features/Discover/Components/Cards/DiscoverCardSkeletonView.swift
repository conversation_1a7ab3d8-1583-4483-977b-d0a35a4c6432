import SwiftUI

/// 发现页卡片骨架屏组件
///
/// 完全匹配实际卡片结构的加载占位符，提供流畅的加载体验
///
/// 特性：
/// - 🎯 精确匹配实际卡片布局
/// - ✨ 流畅的 shimmer 动画效果
/// - 🎲 随机化的文本长度，增加真实感
/// - 🔧 高性能的一次性随机值生成
/// - 🎨 精致的视觉细节和渐变效果
struct DiscoverCardSkeletonView: View {

    // MARK: - 配置选项

    /// 是否启用动画（默认启用）
    let isAnimated: Bool

    /// 动画持续时间（默认 1.5 秒）
    let animationDuration: Double

    // MARK: - 私有属性

    /// 标题第二行宽度（一次性随机生成）
    private let titleSecondLineWidth: CGFloat = [0.6, 0.7, 0.8].randomElement() ?? 0.7

    /// 描述第二行宽度（一次性随机生成）
    private let descriptionSecondLineWidth: CGFloat = [0.65, 0.75, 0.85].randomElement() ?? 0.75

    /// 随机延迟（一次性生成，避免重复计算）
    private let shimmerDelay: Double = Double.random(in: 0...0.4)

    // MARK: - 环境变量

    @Environment(\.colorScheme) private var colorScheme

    // MARK: - 初始化

    /// 创建骨架屏视图
    /// - Parameters:
    ///   - isAnimated: 是否启用 shimmer 动画
    ///   - animationDuration: 动画持续时间
    init(isAnimated: Bool = true, animationDuration: Double = 1.5) {
        self.isAnimated = isAnimated
        self.animationDuration = animationDuration
    }

    // MARK: - 颜色计算属性

    /// 图片区域背景色 - 深色模式优化
    private var imageBackgroundColor: Color {
        colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6)
    }

    /// 图片占位符图标颜色 - 深色模式优化
    private var imagePlaceholderColor: Color {
        colorScheme == .dark ? Color(.systemGray3) : Color(.systemGray4)
    }

    /// 渐变叠加颜色 - 深色模式优化
    private var gradientOverlayColor: Color {
        colorScheme == .dark ? Color(.systemGray4) : Color(.systemGray5)
    }

    /// 标题骨架颜色 - 深色模式优化
    private var titleSkeletonColor: Color {
        colorScheme == .dark ? Color(.systemGray4) : Color(.systemGray5)
    }

    /// 描述骨架颜色 - 深色模式优化
    private var descriptionSkeletonColor: Color {
        colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6)
    }

    // MARK: - Body

    var body: some View {
        VStack(spacing: 0) {
            imageSkeletonSection
            contentSkeletonSection
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .conditionalShimmer(
            isActive: isAnimated,
            duration: animationDuration,
            delay: shimmerDelay
        )
    }
}

// MARK: - 子视图组件

extension DiscoverCardSkeletonView {

    /// 图片骨架区域
    private var imageSkeletonSection: some View {
        Rectangle()
            .fill(imageBackgroundColor)
            .aspectRatio(4/3, contentMode: .fit)
            .overlay(
                // 图片占位符图标
                Image(systemName: "photo")
                    .font(.system(size: 24, weight: .light))
                    .foregroundColor(imagePlaceholderColor)
                    .opacity(colorScheme == .dark ? 0.8 : 0.6)
            )
            .overlay(
                // 添加微妙的渐变效果，增强视觉层次
                LinearGradient(
                    colors: [
                        Color.clear,
                        gradientOverlayColor.opacity(colorScheme == .dark ? 0.4 : 0.3)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
    }

    /// 内容骨架区域
    private var contentSkeletonSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            titleSkeleton
            descriptionSkeleton
        }
        .padding(.horizontal, 12)
        .padding(.top, 8)
        .padding(.bottom, 12)
    }

    /// 标题骨架（两行）
    private var titleSkeleton: some View {
        TextSkeletonView(
            lineHeight: 18,
            color: titleSkeletonColor,
            secondLineWidth: titleSecondLineWidth
        )
        .padding(.bottom, 2)
    }

    /// 描述骨架（两行）
    private var descriptionSkeleton: some View {
        TextSkeletonView(
            lineHeight: 16,
            color: descriptionSkeletonColor,
            secondLineWidth: descriptionSecondLineWidth
        )
    }
}

// MARK: - 通用文本骨架组件

/// 通用的两行文本骨架组件
private struct TextSkeletonView: View {
    let lineHeight: CGFloat
    let color: Color
    let secondLineWidth: CGFloat

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 第一行（全宽）
            RoundedRectangle(cornerRadius: 3)
                .fill(color)
                .frame(height: lineHeight)
                .frame(maxWidth: .infinity)

            // 第二行（部分宽度）
            RoundedRectangle(cornerRadius: 3)
                .fill(color)
                .frame(height: lineHeight)
                .frame(maxWidth: secondLineWidth)
        }
    }
}

// MARK: - View 扩展

extension View {
    /// 条件性应用 shimmer 效果
    /// - Parameters:
    ///   - isActive: 是否激活动画
    ///   - duration: 动画持续时间
    ///   - delay: 动画延迟
    /// - Returns: 应用了条件 shimmer 的视图
    @ViewBuilder
    func conditionalShimmer(
        isActive: Bool,
        duration: Double = 1.5,
        delay: Double = 0.2
    ) -> some View {
        if isActive {
            self.shimmer(isActive: true, duration: duration, delay: delay)
        } else {
            self
        }
    }
}

#Preview("骨架屏网格 - 默认动画") {
    LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2),
        spacing: 12
    ) {
        ForEach(0..<4, id: \.self) { _ in
            DiscoverCardSkeletonView()
        }
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}

#Preview("骨架屏网格 - 无动画") {
    LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2),
        spacing: 12
    ) {
        ForEach(0..<4, id: \.self) { _ in
            DiscoverCardSkeletonView(isAnimated: false)
        }
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}

#Preview("单个骨架屏 - 快速动画") {
    DiscoverCardSkeletonView(animationDuration: 0.8)
        .frame(width: 180)
        .padding()
        .background(Color(.systemGroupedBackground))
}

#Preview("单个骨架屏 - 慢速动画") {
    DiscoverCardSkeletonView(animationDuration: 2.5)
        .frame(width: 180)
        .padding()
        .background(Color(.systemGroupedBackground))
}
