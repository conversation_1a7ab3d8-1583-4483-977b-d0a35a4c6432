import SwiftUI

/// 目的地选择弹窗组件
///
/// 提供目的地搜索、筛选和选择功能的模态弹窗界面。
/// 采用统一的设计语言，与应用其他页面保持视觉一致性。
///
/// 主要功能：
/// - 目的地搜索和筛选
/// - 分类浏览（国内/国际）
/// - 省份/城市层级选择
/// - 收藏和行程筛选
struct DestinationPickerView: View {
    @ObservedObject var viewModel: DiscoverViewModel
    @Binding var isPresented: Bool
    @Binding var selectedLocation: String
    @State private var searchText: String = ""
    @State private var selectedTabId: String? = nil
    @State private var selectedCategoryId: String? = nil

    @State private var allDestinations: [Destination] = []
    @State private var isLoading: Bool = false
    @State private var error: String? = nil
    @State private var showLikedOnly: Bool = false
    @State private var showJourneyOnly: Bool = false

    // Service层依赖
    private let destinationService: DestinationServiceProtocol

    // MARK: - Initialization

    init(
        viewModel: DiscoverViewModel,
        isPresented: Binding<Bool>,
        selectedLocation: Binding<String>,
        destinationService: DestinationServiceProtocol = DestinationService.shared
    ) {
        self.viewModel = viewModel
        self._isPresented = isPresented
        self._selectedLocation = selectedLocation
        self.destinationService = destinationService
    }

    var body: some View {
        let currentCategories = categories
        VStack(spacing: 0) {
            // 顶部标题栏
            ZStack {
                Text("选择探索目的地")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.primary)

                HStack {
                    Spacer()
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            isPresented = false
                        }
                    }) {
                        ZStack {
                            Circle()
                                .fill(Color(.systemGray6))
                                .frame(width: 32, height: 32)

                            Image(systemName: "xmark")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(.secondary)
                        }
                    }
                    .buttonStyle(ScaleButtonStyle())
                    .padding(.trailing, 16)
                }
            }
            .padding(.top, 20)
            .padding(.bottom, 16)
            .background(
                Color(.systemBackground)
                    .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
            )

            // 搜索栏
            DestinationSearchBar(text: $searchText)
                .padding(.horizontal, 16) // 减少水平内边距
                .padding(.top, 10)
                .padding(.bottom, 6)

            // 筛选按钮区域
            HStack(spacing: 10) {
                // 精选按钮
                FilterButton(
                    title: "精选",
                    isSelected: selectedLocation == "精选" && !showLikedOnly,
                    action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            selectedLocation = "精选"
                            showLikedOnly = false
                            isPresented = false
                            viewModel.updateSelectedDestination(nil, showLikedOnly: false, showJourneyOnly: showJourneyOnly)
                        }
                    }
                )

                // 喜欢按钮
                FilterButton(
                    title: "喜欢",
                    isSelected: showLikedOnly,
                    action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            showLikedOnly.toggle()
                            if showLikedOnly {
                                selectedLocation = "喜欢"
                                isPresented = false
                                viewModel.updateSelectedDestination(nil, showLikedOnly: true, showJourneyOnly: showJourneyOnly)
                            } else {
                                selectedLocation = "精选"
                                viewModel.updateSelectedDestination(nil, showLikedOnly: false, showJourneyOnly: showJourneyOnly)
                            }
                        }
                    }
                )
                
                Spacer()
                
                // 包含行程按钮
                CheckboxFilterButton(
                    title: "包含行程",
                    isSelected: showJourneyOnly,
                    action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            showJourneyOnly.toggle()
                            viewModel.updateSelectedDestination(nil, showLikedOnly: showLikedOnly, showJourneyOnly: showJourneyOnly, updateDestination: false)
                        }
                    }
                )
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 12)

            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 0.5)
                .padding(.horizontal, 16)
                .padding(.bottom, 8)

            // Tab分组栏
            HStack(spacing: DesignSystemConstants.Spacing.large) {
                ForEach(tabs, id: \.id) { tab in
                    Button(action: {
                        if tab.name.contains("国际") {
                            ToastManager.shared.show("国际目的地即将上线", style: .info)
                        } else {
                            withAnimation(DesignSystemConstants.standardEaseAnimation) {
                                selectedTabId = tab.id
                                selectedCategoryId = categories.first?.id
                            }
                        }
                    }) {
                        VStack(spacing: 2) {
                            Text(tab.name)
                                .font(.system(size: 15, weight: selectedTabId == tab.id ? .bold : .regular))
                                .foregroundColor(selectedTabId == tab.id ? Color.accentColor : .secondary)
                                .lineLimit(1)
                                .fixedSize(horizontal: true, vertical: false)

                            if selectedTabId == tab.id {
                                RoundedRectangle(cornerRadius: 1)
                                    .fill(Color.accentColor)
                                    .frame(height: 2)
                            } else {
                                RoundedRectangle(cornerRadius: 1)
                                    .fill(.clear)
                                    .frame(height: 2)
                            }
                        }
                        .padding(.vertical, 4)
                        .padding(.horizontal, 4)
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.vertical, 4)
            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.large, style: .continuous))
            .shadow(color: .black.opacity(0.05), radius: DesignSystemConstants.Shadow.standard.radius, x: DesignSystemConstants.Shadow.standard.offset.width, y: DesignSystemConstants.Shadow.standard.offset.height)
            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
            .padding(.bottom, 8)

            // 省份/国家-城市分组内容
            HStack(alignment: .top, spacing: 0) {
                if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    ProvinceSidebarView(
                        categories: currentCategories,
                        selectedCategoryId: selectedCategoryId,
                        onSelect: { id in
                            withAnimation(DesignSystemConstants.standardEaseAnimation) {
                                selectedCategoryId = id
                            }
                        }
                    )
                    .frame(width: 80)
                }
                // 右侧城市网格
                ScrollView(.vertical, showsIndicators: false) {
                    if let error = error {
                        VStack(spacing: DesignSystemConstants.Spacing.small) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.orange)
                            Text(error)
                                .font(.system(size: 15, weight: .medium))
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(DesignSystemConstants.Spacing.large)
                    } else if !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && filteredCities.isEmpty {
                        VStack(spacing: DesignSystemConstants.Spacing.small) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 24, weight: .medium))
                                .foregroundColor(.secondary)
                            Text("未找到相关目的地")
                                .font(.system(size: 15, weight: .medium))
                                .foregroundColor(.secondary)
                        }
                        .padding(DesignSystemConstants.Spacing.large)
                    } else {
                        DestinationCityGridView(
                            province: filteredProvince,
                            cities: filteredCities.map { $0.name },
                            showProvinceTag: searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? isDomesticTab : false,
                            selectedLocation: $selectedLocation,
                            isPresented: $isPresented,
                            onSelect: { name in
                                selectedLocation = name
                                if let dest = allDestinations.first(where: { $0.name == name }) {
                                    viewModel.updateSelectedDestination(dest, showLikedOnly: showLikedOnly, showJourneyOnly: showJourneyOnly)
                                }
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    isPresented = false
                                }
                            }
                        )
                        .padding(.horizontal, DesignSystemConstants.Spacing.small)
                        .padding(.vertical, DesignSystemConstants.Spacing.small)
                    }
                }
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal, DesignSystemConstants.Spacing.standard)
            .padding(.bottom, DesignSystemConstants.Spacing.standard)

            Spacer()
        }
        .background(Color(.systemBackground))
        .ignoresSafeArea(edges: .bottom)
        .onAppear {
            loadAllDestinations()
            // 同步当前的筛选状态
            showLikedOnly = viewModel.showLikedOnly
            showJourneyOnly = viewModel.showJourneyOnly
        }
        .onChange(of: selectedTabId) { _, _ in
            selectedCategoryId = categories.first?.id
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.10).ignoresSafeArea()
                        VStack(spacing: 12) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: Color.blue))
                                .scaleEffect(1.2)
                            Text("加载中…")
                                .font(.system(size: 15, weight: .regular))
                                .foregroundColor(.primary)
                        }
                    }
                }

            }
        )
    }
    
    // MARK: - 计算属性
    private var tabs: [Destination] {
        allDestinations.filter { $0.type == .tab }
    }
    private var categories: [Destination] {
        guard let tabId = selectedTabId else { return [] }
        if let tab = tabs.first(where: { $0.id == tabId }), tab.name.contains("国际") {
            // 国际tab，左侧为国家
            return allDestinations.filter { $0.type == .country && $0.tabId == tabId }
        } else {
            // 国内tab，左侧为省份
            return allDestinations.filter { $0.type == .province && $0.tabId == tabId }
        }
    }
    private var cities: [Destination] {
        guard let catId = selectedCategoryId else { return [] }
        return allDestinations.filter { $0.type == .city && $0.parentId == catId }
    }
    
    private var isDomesticTab: Bool {
        guard let tabId = selectedTabId else { return false }
        if let tab = tabs.first(where: { $0.id == tabId }), tab.name.contains("国际") {
            return false
        }
        return true
    }
    
    private var filteredCities: [Destination] {
        if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return cities
        } else {
            // 使用DestinationService进行搜索
            let searchResults = destinationService.searchDestinations(query: searchText, in: allDestinations)
            return searchResults.filter { dest in
                (dest.type == .city || dest.type == .province || dest.type == .country)
                && dest.tabId == selectedTabId
            }
        }
    }

    private var filteredProvince: String {
        if searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return categories.first(where: { $0.id == selectedCategoryId })?.name ?? ""
        } else {
            return ""
        }
    }
    
    // MARK: - 数据加载
    private func loadAllDestinations() {
        isLoading = true
        error = nil
        Task {
            do {
                let result = try await destinationService.fetchAllDestinations(forceRefresh: false)
                await MainActor.run {
                    self.allDestinations = result
                    self.selectedTabId = result.first(where: { $0.type == .tab })?.id
                    self.selectedCategoryId = categories.first?.id
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = "加载目的地失败"
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - Filter Button Components

private struct FilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: isSelected ? .semibold : .medium))
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isSelected ? Color.accentColor : Color(.secondarySystemBackground))
                        .shadow(color: isSelected ? Color.accentColor.opacity(0.3) : Color.primary.opacity(0.04),
                               radius: 3, x: 0, y: 1)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.secondary.opacity(isSelected ? 0 : 0.1), lineWidth: 0.5)
                )
        }
        .buttonStyle(ScaleButtonStyle())
    }
}

private struct CheckboxFilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: isSelected ? "checkmark.square.fill" : "square")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? Color.accentColor : .secondary)

                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(.systemGray6))
                    .shadow(color: Color.black.opacity(0.03), radius: 1, x: 0, y: 1)
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }
}

// MARK: - Content Components

private struct ProvinceSidebarView: View {
    let categories: [Destination]
    let selectedCategoryId: String?
    let onSelect: (String) -> Void

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: 2) {
                    ForEach(categories.indices, id: \.self) { idx in
                        let category = categories[idx]
                        let isSelected = selectedCategoryId == category.id

                        Button(action: {
                            onSelect(category.id)
                        }) {
                            Text(category.name)
                                .font(.system(size: 14, weight: isSelected ? .semibold : .medium))
                                .foregroundColor(isSelected ? Color.accentColor : .secondary)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, DesignSystemConstants.Spacing.small)
                                .background(
                                    RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.small)
                                        .fill(isSelected ? Color.accentColor.opacity(0.1) : Color.clear)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .id(category.id)
                    }
                }
                .padding(.horizontal, 4)
                .padding(.vertical, DesignSystemConstants.Spacing.small)
            }
            .background(
                RoundedRectangle(cornerRadius: DesignSystemConstants.CornerRadius.default)
                    .fill(Color(.systemGray6))
                    .shadow(color: Color.black.opacity(0.03), radius: 2, x: 0, y: 1)
            )
            .onChange(of: selectedCategoryId) { _, newValue in
                if let newValue = newValue {
                    withAnimation(DesignSystemConstants.standardEaseAnimation) {
                        proxy.scrollTo(newValue, anchor: .center)
                    }
                }
            }
        }
    }
}

private struct DestinationCityGridView: View {
    let province: String
    let cities: [String]
    let showProvinceTag: Bool
    @Binding var selectedLocation: String
    @Binding var isPresented: Bool
    let onSelect: (String) -> Void

    private var allItems: [String] {
        if province.isEmpty {
            return cities
        } else {
            return [province] + cities
        }
    }

    var body: some View {
        let columns = Array(repeating: GridItem(.flexible(minimum: 70), spacing: DesignSystemConstants.Spacing.small), count: 3)

        LazyVGrid(columns: columns, spacing: DesignSystemConstants.Spacing.small) {
            ForEach(allItems.indices, id: \.self) { idx in
                let name = allItems[idx]
                let isSelected = selectedLocation == name
                let isProvince = idx == 0 && !province.isEmpty

                ZStack(alignment: .topTrailing) {
                    Button(action: {
                        onSelect(name)
                    }) {
                        Text(name)
                            .font(.system(size: 14, weight: isSelected ? .semibold : (isProvince ? .semibold : .medium)))
                            .foregroundColor(isSelected ? .white : (isProvince ? Color.accentColor : .primary))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(isSelected ? Color.accentColor : Color(.secondarySystemBackground))
                                    .shadow(color: isSelected ? Color.accentColor.opacity(0.3) : Color.primary.opacity(0.04),
                                           radius: isSelected ? 3 : 3, x: 0, y: isSelected ? 2 : 1)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.secondary.opacity(isSelected ? 0 : 0.1), lineWidth: 0.5)
                            )
                            .padding(.trailing, isProvince ? 20 : 0)
                            .lineLimit(1)
                    }
                    .buttonStyle(ScaleButtonStyle())

                    if isProvince && showProvinceTag {
                        Text(getAdministrativeTypeTag(for: name))
                            .font(.system(size: 9, weight: .bold))
                            .foregroundColor(.white)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(isSelected ? Color.white.opacity(0.9) : Color.accentColor)
                            )
                            .foregroundColor(isSelected ? Color.accentColor : .white)
                            .offset(x: -4, y: 4)
                    }
                }
            }
        }
        .padding(.top, 4)
    }

    /// 根据行政区名称获取对应的类型标签
    /// - Parameter name: 行政区名称
    /// - Returns: 对应的标签文本
    private func getAdministrativeTypeTag(for name: String) -> String {
        // 直辖市
        let municipalities = ["北京", "上海", "天津", "重庆"]
        if municipalities.contains(name) {
            return "市"
        }

        // 自治区（统一显示为省级）
        let autonomousRegions = ["新疆", "西藏", "内蒙古", "广西", "宁夏"]
        if autonomousRegions.contains(name) {
            return "省"
        }

        // 特别行政区
        let specialAdministrativeRegions = ["香港", "澳门"]
        if specialAdministrativeRegions.contains(name) {
            return "特区"
        }

        // 台湾省
        if name == "台湾" {
            return "省"
        }

        // 其他省份（默认）
        return "省"
    }
}
