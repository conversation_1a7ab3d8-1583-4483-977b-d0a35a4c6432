import SwiftUI

struct CommentsSection: View {
    let comments: [DiscoverDetailModels.Comment]
    let commentCount: Int
    @Binding var commentText: String
    let onCommentSubmit: () -> Void
    let isCommentTextValid: Bool

    // 分页相关属性
    let isLoadingComments: Bool
    let hasMoreComments: Bool
    let onLoadMoreComments: () -> Void

    // 回复相关属性
    let loadingRepliesForComment: Set<String>
    let onLoadMoreReplies: (String) -> Void



    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 评论标题
            commentSectionHeader

            // 评论内容区域
            VStack(alignment: .leading, spacing: 12) {
                // 评论输入区域 - 移到顶部
                commentInputSection

                // 评论列表 - 简洁设计
                if comments.isEmpty {
                    emptyCommentsView
                } else {
                    commentsListView
                }
            }
        }
    }

    /// 评论标题
    private var commentSectionHeader: some View {
        HStack {
            HStack(spacing: 4) {
                Image(systemName: "bubble.left.and.bubble.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)

                Text("评论")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)

                if commentCount > 0 {
                    Text("(\(commentCount))")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.secondary)
                }
            }

            Spacer()
        }
    }

    /// 空评论状态视图
    private var emptyCommentsView: some View {
        VStack(spacing: 8) {
            Image(systemName: "bubble.left.and.bubble.right")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.secondary.opacity(0.6))

            Text("还没有评论，来说点什么吧")
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 20)
    }

    /// 评论列表视图
    private var commentsListView: some View {
        LazyVStack(alignment: .leading, spacing: 0) {
            ForEach(Array(comments.enumerated()), id: \.element.id) { index, comment in
                VStack(alignment: .leading, spacing: 0) {
                    CommentView(
                        comment: comment,
                        isLoadingReplies: loadingRepliesForComment.contains(comment.id),
                        onLoadMoreReplies: {
                            onLoadMoreReplies(comment.id)
                        }
                    )
                    .frame(maxWidth: .infinity, alignment: .leading)

                    // 分割线（最后一条评论不显示）
                    if index < comments.count - 1 {
                        Divider()
                            .background(Color(.systemGray6))
                            .padding(.vertical, 8)
                    }
                }
                .onAppear {
                    // 当显示到倒数第3条评论时，触发加载更多
                    if index == comments.count - 3 && hasMoreComments && !isLoadingComments {
                        onLoadMoreComments()
                    }
                }
            }

            // 分页底部视图
            PaginationFooterView.standard(
                isLoadingMore: isLoadingComments,
                hasMoreData: hasMoreComments,
                hasData: !comments.isEmpty
            )
        }
    }

    /// 评论输入区域
    private var commentInputSection: some View {
        HStack(spacing: 10) {
            // 用户头像
            userAvatarView

            // 输入框容器
            HStack(spacing: 6) {
                commentTextField
                sendButton
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(commentInputBackground)
        }
    }

    /// 用户头像视图
    private var userAvatarView: some View {
        Circle()
            .fill(Color(.systemGray5))
            .frame(width: 28, height: 28)
            .overlay(
                Image(systemName: "person.fill")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            )
    }

    /// 评论输入框
    private var commentTextField: some View {
        TextField("写下你的想法...", text: $commentText)
            .font(.system(size: 13, weight: .regular))
            .foregroundColor(.primary)
            .onSubmit {
                onCommentSubmit()
            }
    }

    /// 发送按钮
    private var sendButton: some View {
        Button(action: {
            onCommentSubmit()
        }) {
            Image(systemName: "paperplane.fill")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(isCommentTextValid ? .white : .secondary)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(isCommentTextValid ? Color.blue : Color(.systemGray4))
                )
                .scaleEffect(isCommentTextValid ? 1.0 : 0.9)
                .animation(.easeInOut(duration: 0.2), value: isCommentTextValid)
        }
        .disabled(!isCommentTextValid)
    }

    /// 评论输入框背景
    private var commentInputBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color(.systemGray6))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color(.systemGray5), lineWidth: 0.5)
            )
    }
}

// MARK: - Preview

#Preview("有评论") {
    CommentsSection(
        comments: [
            DiscoverDetailModels.Comment(
                id: "1",
                content: "这个地方真的很美！",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user1",
                    username: "旅行者",
                    avatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
                    location: "北京"
                ),
                createdAt: Date(),
                likeCount: 5,
                isLiked: false,
                replies: [],
                replyCount: 3,
                hasMoreReplies: true
            )
        ],
        commentCount: 1,
        commentText: .constant(""),
        onCommentSubmit: {},
        isCommentTextValid: false,
        isLoadingComments: false,
        hasMoreComments: true,
        onLoadMoreComments: {},
        loadingRepliesForComment: [],
        onLoadMoreReplies: { _ in }
    )
    .padding()
}

#Preview("空状态") {
    CommentsSection(
        comments: [],
        commentCount: 0,
        commentText: .constant(""),
        onCommentSubmit: {},
        isCommentTextValid: false,
        isLoadingComments: false,
        hasMoreComments: false,
        onLoadMoreComments: {},
        loadingRepliesForComment: [],
        onLoadMoreReplies: { _ in }
    )
    .padding()
}
