import SwiftUI

struct CommentView: View {
    let comment: DiscoverDetailModels.Comment
    let isLoadingReplies: Bool
    let onLoadMoreReplies: () -> Void

    @State private var showAllReplies = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 用户头像
            userAvatar

            // 评论内容
            VStack(alignment: .leading, spacing: 4) {
                commentContent

                // 回复列表
                if !comment.replies.isEmpty {
                    repliesSection
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.vertical, 6)

    }

    private var userAvatar: some View {
        AsyncImage(url: URL(string: comment.author.avatarUrl ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Circle()
                .fill(Color(.systemGray5))
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                )
        }
        .frame(width: 32, height: 32)
        .clipShape(Circle())
    }

    private var commentContent: some View {
        VStack(alignment: .leading, spacing: 4) {
            // 用户名和评论内容在同一行，支持多行显示
            (Text(comment.author.username)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(.secondary)
            + Text(": ")
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(.primary)
            + Text(comment.content)
                .font(.system(size: 13, weight: .regular))
                .foregroundColor(.primary))
                .lineLimit(nil) // 允许多行显示
                .fixedSize(horizontal: false, vertical: true) // 确保垂直方向自适应

            // 时间、归属地和回复按钮
            commentMetaInfo
        }
    }

    /// 评论元信息（时间、归属地、回复按钮）
    private var commentMetaInfo: some View {
        HStack(spacing: 8) {
            Text(comment.createdAt.commentTimeDescription)
                .font(.system(size: 11, weight: .regular))
                .foregroundColor(.secondary)

            if let location = comment.author.location {
                Text(location)
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button("回复") {
                // TODO: 实现回复功能
            }
            .font(.system(size: 11, weight: .regular))
            .foregroundColor(.secondary)
        }
    }

    /// 回复区域
    private var repliesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 显示的回复列表
            let displayedReplies = showAllReplies ? comment.replies : Array(comment.replies.prefix(1))

            ForEach(Array(displayedReplies.enumerated()), id: \.element.id) { index, reply in
                ReplyView(reply: reply)
                    .opacity(replyOpacity(for: index))
                    .scaleEffect(replyScale(for: index))
                    .animation(
                        .easeInOut(duration: 0.3)
                        .delay(Double(index) * 0.05),
                        value: showAllReplies
                    )
            }

            if shouldShowExpandButton {
                if showAllReplies {
                    if canLoadMoreReplies {
                        loadMoreRepliesButton
                            .transition(.opacity)
                    }
                    collapseButton
                        .transition(.opacity)
                } else {
                    expandMoreButton
                        .transition(.opacity)
                }
            }
        }
        .padding(.top, 8)
    }

    private var shouldShowExpandButton: Bool {
        return totalReplyCount > 1
    }

    private var totalReplyCount: Int {
        return comment.replyCount ?? comment.replies.count
    }

    private var canLoadMoreReplies: Bool {
        return comment.hasMoreReplies ?? false
    }

    private func replyOpacity(for index: Int) -> Double {
        showAllReplies ? 1.0 : (index == 0 ? 1.0 : 0.0)
    }

    private func replyScale(for index: Int) -> Double {
        showAllReplies ? 1.0 : (index == 0 ? 1.0 : 0.95)
    }

    private var expandMoreButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                showAllReplies = true

                if comment.replies.isEmpty && (comment.replyCount ?? 0) > 0 {
                    onLoadMoreReplies()
                }
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.down")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.secondary)
                    .rotationEffect(.degrees(showAllReplies ? 180 : 0))
                    .animation(.easeInOut(duration: 0.2), value: showAllReplies)

                Text("展开更多回复")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }

    private var loadMoreRepliesButton: some View {
        Button(action: {
            onLoadMoreReplies()
        }) {
            HStack(spacing: 4) {
                if isLoadingReplies {
                    ProgressView()
                        .scaleEffect(0.6)
                        .frame(width: 10, height: 10)
                        .transition(.opacity)
                } else {
                    Image(systemName: "arrow.down.circle")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.secondary)
                        .transition(.opacity)
                }

                Text(isLoadingReplies ? "加载中..." : "加载更多回复")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.secondary)
                    .animation(.easeInOut(duration: 0.2), value: isLoadingReplies)
            }
            .scaleEffect(isLoadingReplies ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isLoadingReplies)
        }
        .disabled(isLoadingReplies)
        .padding(.vertical, 4)
    }

    private var collapseButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                showAllReplies = false
            }
        }) {
            HStack(spacing: 4) {
                Image(systemName: "chevron.up")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.secondary)

                Text("收起回复")
                    .font(.system(size: 12, weight: .regular))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    VStack(spacing: 16) {
        CommentView(
            comment: DiscoverDetailModels.Comment(
                id: "1",
                content: "这是一条很棒的评论！",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user1",
                    username: "张三",
                    avatarUrl: nil,
                    location: "北京"
                ),
                createdAt: Date(),
                likeCount: 5,
                isLiked: false,
                replies: [],
                replyCount: 0,
                hasMoreReplies: false
            ),
            isLoadingReplies: false,
            onLoadMoreReplies: {}
        )

        CommentView(
            comment: DiscoverDetailModels.Comment(
                id: "2",
                content: "这是一条比较长的评论，用来测试多行文本的显示效果。这里有更多的内容来展示评论的完整布局。",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user2",
                    username: "李四",
                    avatarUrl: nil,
                    location: "上海"
                ),
                createdAt: Date(),
                likeCount: 12,
                isLiked: true,
                replies: [],
                replyCount: 5,
                hasMoreReplies: true
            ),
            isLoadingReplies: false,
            onLoadMoreReplies: {}
        )

        CommentView(
            comment: DiscoverDetailModels.Comment(
                id: "3",
                content: "简短评论",
                author: DiscoverDetailModels.CommentAuthor(
                    id: "user3",
                    username: "王五",
                    avatarUrl: nil,
                    location: "广州"
                ),
                createdAt: Date(),
                likeCount: 0,
                isLiked: false,
                replies: [],
                replyCount: 0,
                hasMoreReplies: false
            ),
            isLoadingReplies: true,
            onLoadMoreReplies: {}
        )
    }
    .padding()
}
