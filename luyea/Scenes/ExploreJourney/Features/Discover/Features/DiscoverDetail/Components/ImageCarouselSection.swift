import SwiftUI

struct ImageCarouselSection: View {
    let imageUrls: [String]
    @Binding var currentPage: Int
    @Binding var isAutoScrolling: Bool
    let authorAvatarUrl: String
    let authorUsername: String
    let onImageTap: () -> Void
    let onAuthorTap: () -> Void
    let onZoomChanged: (Bool) -> Void
    var body: some View {
        ZStack(alignment: .bottom) {
            ImageCarouselView(
                imageUrls: imageUrls,
                currentPage: $currentPage,
                isAutoScrolling: $isAutoScrolling,
                onImageTap: onImageTap,
                onZoomChanged: onZoomChanged
            )

            HStack {
                AuthorInfoView(
                    avatarUrl: authorAvatarUrl,
                    username: authorUsername,
                    onTap: onAuthorTap
                )

                Spacer()

                PageIndicatorView(
                    totalPages: imageUrls.count,
                    currentPage: currentPage
                )
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .frame(height: 380)
        .ignoresSafeArea()
    }
}

// MARK: - Preview

#Preview("单张图片") {
    ImageCarouselSection(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4"
        ],
        currentPage: .constant(0),
        isAutoScrolling: .constant(true),
        authorAvatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
        authorUsername: "旅行摄影师",
        onImageTap: {},
        onAuthorTap: {},
        onZoomChanged: { _ in }
    )
}

#Preview("多张图片") {
    ImageCarouselSection(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e"
        ],
        currentPage: .constant(1),
        isAutoScrolling: .constant(false),
        authorAvatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
        authorUsername: "风景摄影达人",
        onImageTap: {},
        onAuthorTap: {},
        onZoomChanged: { _ in }
    )
}
