import SwiftUI

// MARK: - ImageCarouselView

/// 图片轮播组件
///
/// **功能概述:**
/// - 支持多张图片的轮播展示
/// - 自动轮播功能，可手动控制开关
/// - 图片点击事件处理
/// - 缓存异步图片加载
/// - 错误状态和加载状态处理
///
/// **技术特性:**
/// - 使用TabView实现页面切换效果
/// - Task-based自动轮播控制
/// - GeometryReader响应式布局
/// - CachedAsyncImage优化图片加载性能
/// - 支持缩放状态回调
struct ImageCarouselView: View {

    // MARK: - Properties

    /// 图片URL数组
    let imageUrls: [String]
    /// 当前页面索引
    @Binding var currentPage: Int
    /// 是否启用自动滚动
    @Binding var isAutoScrolling: Bool
    /// 图片点击回调
    let onImageTap: () -> Void
    /// 缩放状态变化回调
    let onZoomChanged: (Bool) -> Void

    // MARK: - Private Properties

    /// 自动轮播的时间间隔（秒）
    private let autoScrollInterval: TimeInterval = 3.0
    /// 自动滚动任务
    @State private var autoScrollTask: Task<Void, Never>? = nil
    /// 是否正在执行自动滚动（用于区分手动和自动操作）
    @State private var isPerformingAutoScroll = false
    /// 手动滑动后的恢复延迟（秒）
    private let manualScrollRecoveryDelay: TimeInterval = 5.0
    /// 用户是否正在手动滑动
    @State private var isUserInteracting = false

    // MARK: - Body

    var body: some View {
        GeometryReader { geometry in
            carouselTabView(geometry: geometry)
        }
        .onAppear {
            setupAutoScrollIfNeeded()
        }
        .onDisappear {
            stopAutoScroll()
        }
        .onChange(of: isAutoScrolling) { _, newValue in
            handleAutoScrollToggle(newValue)
        }
    }

    /// 轮播TabView
    /// - Parameter geometry: 几何信息
    /// - Returns: TabView组件
    private func carouselTabView(geometry: GeometryProxy) -> some View {
        TabView(selection: $currentPage) {
            ForEach(0..<imageUrls.count, id: \.self) { index in
                imageItem(at: index, geometry: geometry)
            }
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        .onChange(of: currentPage) { oldValue, newValue in
            handlePageChange(from: oldValue, to: newValue)
        }
        .gesture(
            DragGesture()
                .onChanged { _ in
                    // 用户开始手动滑动
                    if !isUserInteracting {
                        isUserInteracting = true
                        // 立即停止自动滚动，避免冲突
                        if isAutoScrolling {
                            stopAutoScroll()
                        }
                    }
                }
                .onEnded { _ in
                    // 用户结束手动滑动
                    isUserInteracting = false
                    // 延迟恢复自动滚动
                    if isAutoScrolling {
                        pauseAutoScrollTemporarily()
                    }
                }
        )
    }

    // MARK: - Image Item Components

    /// 单个图片项
    /// - Parameters:
    ///   - index: 图片索引
    ///   - geometry: 几何信息
    /// - Returns: 图片视图
    private func imageItem(at index: Int, geometry: GeometryProxy) -> some View {
        CachedAsyncImage(
            url: URL(string: imageUrls[index])
        ) { image in
            // 成功加载的图片
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: geometry.size.width, height: 380)
                .clipped()
        } placeholder: {
            // 加载中的占位符
            loadingPlaceholder(geometry: geometry)
        } errorView: {
            // 加载失败的错误视图
            errorPlaceholder(geometry: geometry)
        }
        .frame(width: geometry.size.width)
        .tag(index)
        .onTapGesture {
            onImageTap()
        }
    }

    // MARK: - Placeholder Components

    /// 加载中占位符
    /// - Parameter geometry: 几何信息
    /// - Returns: 占位符视图
    private func loadingPlaceholder(geometry: GeometryProxy) -> some View {
        Rectangle()
            .fill(Color.gray.opacity(0.1))
            .frame(width: geometry.size.width, height: 380)
            .overlay(
                ProgressView()
                    .tint(.gray)
            )
    }

    /// 错误占位符
    /// - Parameter geometry: 几何信息
    /// - Returns: 错误视图
    private func errorPlaceholder(geometry: GeometryProxy) -> some View {
        ImageErrorView(
            style: .custom(
                backgroundColor: Color.blue.opacity(0.06),
                iconColor: Color.blue.opacity(0.5)
            ),
            showText: true,
            errorText: "图片加载失败",
            iconName: "photo"
        )
        .frame(width: geometry.size.width, height: 380)
    }



    // MARK: - Helper Methods

    /// 设置自动滚动（如果需要）
    private func setupAutoScrollIfNeeded() {
        if imageUrls.count > 1 {
            startAutoScroll()
        }
    }

    /// 处理自动滚动开关切换
    /// - Parameter newValue: 新的自动滚动状态
    private func handleAutoScrollToggle(_ newValue: Bool) {
        if newValue && imageUrls.count > 1 {
            startAutoScroll()
        } else {
            stopAutoScroll()
        }
    }

    /// 处理页面变化
    /// - Parameters:
    ///   - oldValue: 旧的页面索引
    ///   - newValue: 新的页面索引
    private func handlePageChange(from oldValue: Int, to newValue: Int) {
        // 验证并更新当前页面索引
        let validPage = max(0, min(newValue, imageUrls.count - 1))
        if validPage != newValue {
            currentPage = validPage
            return
        }

        // 页面变化处理已经通过手势检测处理，这里只做基本验证
    }

    /// 暂时暂停自动滚动（用户手动滑动后）
    private func pauseAutoScrollTemporarily() {
        // 停止当前的自动滚动
        stopAutoScroll()

        // 延迟恢复自动滚动
        Task {
            try? await Task.sleep(nanoseconds: UInt64(manualScrollRecoveryDelay * 1_000_000_000))

            // 检查是否仍需要自动滚动
            if isAutoScrolling && imageUrls.count > 1 {
                await MainActor.run {
                    startAutoScroll()
                }
            }
        }
    }

    // MARK: - Auto Scroll Control

    /// 开始自动滚动
    ///
    /// **实现原理:**
    /// - 使用Task创建异步任务
    /// - 定时器间隔为3秒
    /// - 循环切换到下一页，到最后一页时回到第一页
    /// - 支持任务取消机制
    /// - 使用标记位区分自动和手动操作
    private func startAutoScroll() {
        // 先停止现有的自动滚动任务
        stopAutoScroll()

        autoScrollTask = Task {
            while !Task.isCancelled {
                // 等待指定的时间间隔
                try? await Task.sleep(nanoseconds: UInt64(autoScrollInterval * 1_000_000_000))

                // 检查任务是否被取消、自动滚动是否启用、用户是否正在交互
                if !Task.isCancelled && isAutoScrolling && !isUserInteracting {
                    await MainActor.run {
                        // 再次检查用户是否开始交互（双重检查）
                        if !isUserInteracting {
                            // 标记正在执行自动滚动
                            isPerformingAutoScroll = true

                            // 使用动画更新页面索引，确保完整的滚动效果
                            withAnimation(.easeInOut(duration: 0.5)) {
                                currentPage = (currentPage + 1) % imageUrls.count
                            }

                            // 延迟后重置标记，确保动画完成
                            Task {
                                try? await Task.sleep(nanoseconds: 600_000_000) // 0.6秒，略长于动画时间
                                isPerformingAutoScroll = false
                            }
                        }
                    }
                }
            }
        }
    }

    /// 停止自动滚动
    ///
    /// **功能:**
    /// - 取消当前的自动滚动任务
    /// - 清理任务引用，避免内存泄漏
    private func stopAutoScroll() {
        autoScrollTask?.cancel()
        autoScrollTask = nil
    }
}

// MARK: - 预览
#Preview {
    ImageCarouselView(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e"
        ],
        currentPage: .constant(0),
        isAutoScrolling: .constant(true),
        onImageTap: {},
        onZoomChanged: { _ in }
    )
    .frame(height: 380)
}
