import SwiftUI

/// 旅程路线卡片视图
/// 复刻首页行程卡片设计，简洁版本
struct JourneyRouteCardView: View {

    // MARK: - Properties

    let journey: DiscoverDetailModels.JourneyRoute
    let onViewTap: () -> Void
    let onForkTap: () -> Void

    // MARK: - Constants

    private enum ImageConstants {
        static let width: CGFloat = 120
        static let minHeight: CGFloat = 80
        static let cornerRadius: CGFloat = 8
    }

    // MARK: - Body

    var body: some View {
        VStack(spacing: 12) {
            // 相关旅程小标题
            relatedRouteSectionHeader

            // 路线卡片
            VStack(spacing: 0) {
                HStack(alignment: .top, spacing: 12) {
                    // 左侧：路线封面图片
                    routeCoverImageView

                    // 中间：路线信息 - 使用VStack清晰布局
                    VStack(alignment: .leading, spacing: 6) {
                        // 上半部分：标题、目的地信息和查看按钮
                        HStack(alignment: .top, spacing: 8) {
                            VStack(alignment: .leading, spacing: 6) {
                                // 标题
                                Text(journey.title)
                                    .font(.system(size: 15, weight: .semibold))
                                    .foregroundColor(.primary)
                                    .lineLimit(1)
                                    .truncationMode(.tail)
                                    .layoutPriority(1) // 优先使用可用空间

                                // 目的地信息
                                destinationInfoView
                            }
                            .layoutPriority(1) // VStack优先获得空间
                            .frame(maxWidth: .infinity, alignment: .leading)

                            // 右侧：查看按钮（固定宽度，不压缩文字）
                            viewButton
                                .layoutPriority(0) // 按钮不压缩文字区域
                        }

                        // 天数和日期信息
                        durationAndDateView

                        // 状态标签和Fork按钮
                        HStack(alignment: .center, spacing: 8) {
                            statusBadgeView

                            Spacer()

                            // 右下角复制行程按钮
                            forkButton
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gray.opacity(0.1), lineWidth: 1)
            )
        }
    }

    // MARK: - Private Views

    /// 相关旅程小标题
    private var relatedRouteSectionHeader: some View {
        HStack {
            HStack(spacing: 4) {
                Image(systemName: "map")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)

                Text("相关旅程")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
            }

            Spacer()
        }
    }

    /// 路线封面图片视图
    private var routeCoverImageView: some View {
        ZStack {
            AsyncImage(url: URL(string: getRouteImageUrl())) { image in
                image
                    .resizable()
                    .scaledToFill()
            } placeholder: {
                // 星空背景占位图
                LinearGradient(
                    colors: [
                        Color.blue.opacity(0.8),
                        Color.purple.opacity(0.6),
                        Color.black.opacity(0.9)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
            .frame(width: ImageConstants.width)
            .frame(minHeight: ImageConstants.minHeight)
            .frame(maxHeight: 100) // 限制最大高度，避免过高
            .clipShape(RoundedRectangle(cornerRadius: ImageConstants.cornerRadius))


        }
    }

    /// 目的地信息视图
    private var destinationInfoView: some View {
        HStack(spacing: 6) {
            Image(systemName: "location.fill")
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.blue)

            Text(getDestinationText())
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.blue)
                .lineLimit(1)
                .truncationMode(.tail)

            Spacer()
        }
    }

    /// 天数和日期信息视图
    private var durationAndDateView: some View {
        HStack(spacing: 12) {
            // 天数（显示完整格式，如"6天"）
            Text("\(journey.duration)天")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.orange)

            // 城市和地点数量
            Text(getCityAndAttractionText())
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.secondary)
                .lineLimit(1)
                .truncationMode(.tail)

            Spacer()
        }
    }

    /// 状态标签视图
    private var statusBadgeView: some View {
        HStack(spacing: 4) {
            if let forkCount = journey.forkCount, forkCount > 0 {
                HStack(spacing: 3) {
                    Image(systemName: "arrow.branch")
                        .font(.system(size: 9, weight: .medium))
                        .foregroundColor(.secondary)

                    Text("\(forkCount)")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(Color.secondary.opacity(0.1))
                .clipShape(Capsule())
            }
        }
    }

    /// 查看按钮（垂直布局，图标在上，文字在下）
    private var viewButton: some View {
        Button(action: onViewTap) {
            VStack(spacing: 2) {
                Image(systemName: "eye.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)

                Text("查看")
                    .font(.system(size: 9, weight: .medium))
                    .foregroundColor(.blue)
            }
            .padding(.leading, 6)
        }
        .buttonStyle(ViewButtonStyle())
        .accessibilityLabel("查看路线")
        .fixedSize() // 使用按钮内容的自然尺寸，最小化占用空间
    }



    /// 右下角Fork行程按钮
    private var forkButton: some View {
        Button(action: onForkTap) {
            HStack(spacing: 3) {
                Image(systemName: "doc.on.doc")
                    .font(.system(size: 10, weight: .medium))

                Text("Fork行程")
                    .font(.system(size: 10, weight: .medium))
            }
            .foregroundColor(.blue)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.blue.opacity(0.08))
            .clipShape(Capsule())
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Private Methods

    /// 获取路线图片URL
    private func getRouteImageUrl() -> String {
        // 使用第一个目的地的图片，或者返回默认图片
        return journey.destinations.first?.imageUrl ?? ""
    }

    /// 获取目的地文本（优化单行显示）
    private func getDestinationText() -> String {
        let destinationNames = journey.destinations.map { $0.name }
        return destinationNames.joined(separator: " → ")
    }

    /// 获取城市和地点数量文本（简化准确的计算逻辑）
    private func getCityAndAttractionText() -> String {
        // 简化城市统计：提取主要城市名称
        let uniqueCities = Set(journey.destinations.compactMap { destination in
            let location = destination.location

            // 提取城市名称的简化逻辑
            if location.contains("成都") {
                return "成都"
            } else if location.contains("北京") {
                return "北京"
            } else if location.contains("拉萨") {
                return "拉萨"
            } else if location.contains("康定") {
                return "康定"
            } else if location.contains("理塘") {
                return "理塘"
            } else if location.contains("八宿") {
                return "八宿"
            } else if location.contains("昌都") {
                return "昌都"
            } else {
                // 兜底：提取最后一个地名
                let components = location.components(separatedBy: CharacterSet(charactersIn: "省市区县州"))
                return components.last?.trimmingCharacters(in: .whitespaces) ?? destination.name
            }
        })

        // 统计总地点数量：目的地本身 + 所有景点
        let totalDestinations = journey.destinations.count
        let totalAttractions = journey.destinations.reduce(0) { total, destination in
            total + destination.attractions.count
        }
        let totalPlaces = totalDestinations + totalAttractions

        return "\(uniqueCities.count)个城市\(totalPlaces)个地点"
    }
}

// MARK: - Custom Button Styles

/// 查看按钮的自定义样式，提供点击效果
struct ViewButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.85 : 1.0)
            .opacity(configuration.isPressed ? 0.7 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Preview

#if DEBUG
struct JourneyRouteCardView_Previews: PreviewProvider {
    static var previews: some View {
        let mockJourney = DiscoverDetailModels.JourneyRoute(
            id: "route_001",
            title: "日本关西樱花季",
            content: "专为樱花季设计的关西深度游路线",
            duration: 6,
            destinations: [
                DiscoverDetailModels.RouteDestination(
                    id: "dest_001",
                    name: "东京",
                    location: "东京都",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 35.6762, longitude: 139.6503),
                    order: 1,
                    stayDuration: "2天",
                    content: "日本首都",
                    imageUrl: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf",
                    attractions: [],
                    activities: ["观光", "购物"],
                    tips: ["提前预订酒店"]
                ),
                DiscoverDetailModels.RouteDestination(
                    id: "dest_002",
                    name: "京都",
                    location: "京都府",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 35.0116, longitude: 135.7681),
                    order: 2,
                    stayDuration: "2天",
                    content: "古都京都",
                    imageUrl: "https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e",
                    attractions: [],
                    activities: ["寺庙参观", "传统文化体验"],
                    tips: ["穿舒适的鞋子"]
                ),
                DiscoverDetailModels.RouteDestination(
                    id: "dest_003",
                    name: "大阪",
                    location: "大阪府",
                    coordinate: DiscoverDetailModels.Coordinate(latitude: 34.6937, longitude: 135.5023),
                    order: 3,
                    stayDuration: "2天",
                    content: "美食之都",
                    imageUrl: "https://images.unsplash.com/photo-1590559899731-a382839e5549",
                    attractions: [],
                    activities: ["美食体验", "购物"],
                    tips: ["尝试当地特色小吃"]
                )
            ],
            totalDistance: 500.0,
            estimatedCost: DiscoverDetailModels.CostRange(min: 8000, max: 12000, currency: "CNY"),
            difficulty: .easy,
            forkCount: 256,
            tags: ["樱花", "文化", "美食"],
            highlights: ["樱花盛开", "古都风情", "美食天堂"],
            tips: ["3月下旬至4月上旬为最佳时间"],
            bestSeasons: ["春季"],
            createdAt: Date(),
            updatedAt: Date()
        )

        VStack(spacing: 20) {
            JourneyRouteCardView(
                journey: mockJourney,
                onViewTap: {
                    print("查看按钮被点击")
                },
                onForkTap: {
                    print("复制行程按钮被点击")
                }
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewDisplayName("旅程路线卡片 - 复刻版")
    }
}
#endif
