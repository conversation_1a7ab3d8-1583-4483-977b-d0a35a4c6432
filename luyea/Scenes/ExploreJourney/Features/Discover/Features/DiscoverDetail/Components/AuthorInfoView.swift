import SwiftUI

struct AuthorInfoView: View {
    let avatarUrl: String
    let username: String
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                CachedAsyncImage(
                    url: URL(string: avatarUrl)
                ) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: DiscoverDetailConstants.Layout.authorAvatarSize, height: DiscoverDetailConstants.Layout.authorAvatarSize)
                        .clipShape(Circle())
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: DiscoverDetailConstants.Layout.authorAvatarSize, height: DiscoverDetailConstants.Layout.authorAvatarSize)
                        .overlay(
                            Image(systemName: "person.circle.fill")
                                .font(.title2)
                                .foregroundColor(.gray.opacity(0.6))
                        )
                } errorView: {
                    Circle()
                        .fill(Color.red.opacity(0.1))
                        .frame(width: DiscoverDetailConstants.Layout.authorAvatarSize, height: DiscoverDetailConstants.Layout.authorAvatarSize)
                        .overlay(
                            Image(systemName: "person.circle.fill")
                                .font(.title2)
                                .foregroundColor(.red.opacity(0.6))
                        )
                }

                Text(username)
                    .font(.subheadline) // 调整回.subheadline，大小更加适中
                    .fontWeight(.medium) // 保持medium字重
                    .foregroundColor(.white)
            }
            .padding(.leading, 0)
            .padding(.trailing, 12)
            .frame(height: DiscoverDetailConstants.Layout.authorAvatarSize)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.3))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.blue
            .ignoresSafeArea()
        
        AuthorInfoView(
            avatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e",
            username: "张三",
            onTap: {}
        )
    }
}
