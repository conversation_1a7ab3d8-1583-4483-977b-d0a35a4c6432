import SwiftUI

// MARK: - ContentInfoView

/// 内容信息展示组件
///
/// **功能概述:**
/// - 展示发现内容的标题、描述、位置和话题信息
/// - 采用现代化的卡片式设计，支持话题标签和位置标签
/// - 响应式布局，适配不同屏幕尺寸
///
/// **设计特色:**
/// - 胶囊式话题标签，带有图标和边框效果
/// - 位置信息标签，简洁明了
/// - 优化的文字排版和间距
/// - 支持多行文本显示
struct ContentInfoView: View {

    // MARK: - Properties

    /// 内容标题
    let title: String
    /// 内容描述
    let content: String
    /// 位置信息
    let location: String
    /// 话题信息
    let topic: Topic?
    /// 交互按钮相关
    let isLiked: Bool
    let likeCount: Int
    let onLike: () -> Void
    let onShare: () -> Void

    // MARK: - Body

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题区域
            titleText

            // 内容区域
            if !content.isEmpty {
                contentSection
            }

            // 标签区域（移动到描述下方）
            tagRow
        }
    }

    // MARK: - Component Views



    /// 标题文本
    private var titleText: some View {
        Text(title)
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.primary)
            .multilineTextAlignment(.leading)
            .lineLimit(nil)
            .lineSpacing(1)
            .fixedSize(horizontal: false, vertical: true)
            .frame(maxWidth: .infinity, alignment: .leading)
    }

    /// 标签行（话题和位置标签 + 交互按钮）
    private var tagRow: some View {
        HStack(spacing: 6) {
            // 话题标签（如果有话题信息）
            if let topic = topic {
                topicTag(topic)
            }

            // 位置标签（如果有位置信息）
            if !location.isEmpty {
                locationTag
            }

            Spacer()

            // 交互按钮区域
            interactionButtons
        }
    }

    /// 话题标签
    private func topicTag(_ topic: Topic) -> some View {
        HStack(spacing: 3) {
            Text("#")
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.blue)

            Text(topic.name)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.blue)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color.blue.opacity(0.08))
                .overlay(
                    Capsule()
                        .stroke(Color.blue.opacity(0.2), lineWidth: 0.5)
                )
        )
    }

    /// 位置标签
    private var locationTag: some View {
        HStack(spacing: 3) {
            Image(systemName: "location.fill")
                .font(.system(size: 9, weight: .medium))
                .foregroundColor(.orange)

            Text(location)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(.orange)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(Color.orange.opacity(0.08))
                .overlay(
                    Capsule()
                        .stroke(Color.orange.opacity(0.2), lineWidth: 0.5)
                )
        )
    }

    /// 内容区域
    private var contentSection: some View {
        Text(content)
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.secondary)
            .lineLimit(nil)
            .lineSpacing(2)
            .multilineTextAlignment(.leading)
            .frame(maxWidth: .infinity, alignment: .leading)
    }

    /// 交互按钮区域
    private var interactionButtons: some View {
        HStack(spacing: 8) {
            // 喜欢按钮
            Button(action: onLike) {
                HStack(spacing: 3) {
                    Image(systemName: isLiked ? "heart.fill" : "heart")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(isLiked ? .red : .secondary)
                        .scaleEffect(isLiked ? 1.1 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isLiked)

                    if likeCount > 0 {
                        Text("\(likeCount)")
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(Color.secondary.opacity(0.08))
                        .overlay(
                            Capsule()
                                .stroke(Color.secondary.opacity(0.2), lineWidth: 0.5)
                        )
                )
                .contentShape(Rectangle())
            }
            .buttonStyle(InteractionButtonStyle())

            // 分享按钮
            Button(action: onShare) {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.secondary.opacity(0.08))
                            .overlay(
                                Capsule()
                                    .stroke(Color.secondary.opacity(0.2), lineWidth: 0.5)
                            )
                    )
                    .contentShape(Rectangle())
            }
            .buttonStyle(InteractionButtonStyle())
        }
    }

}

// MARK: - Preview

#Preview("标准内容") {
    ContentInfoView(
        title: "美丽的自然风光探索之旅",
        content: "这是一个非常美丽的地方，有着壮观的山景和清澈的湖水。这里的空气清新，环境优美，是一个非常适合旅游和放松的好地方。在这里你可以感受到大自然的魅力，体验到前所未有的宁静与美好。",
        location: "九寨沟国家级自然保护区",
        topic: Topic(id: "1", name: "自然风光", order: 1),
        isLiked: false,
        likeCount: 128,
        onLike: {},
        onShare: {}
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("无位置信息") {
    ContentInfoView(
        title: "城市探索之旅",
        content: "在这个繁华的都市中，每一个角落都充满了惊喜和故事。现代建筑与传统文化的完美融合，创造出独特的城市魅力。",
        location: "",
        topic: Topic(id: "2", name: "城市探索", order: 2),
        isLiked: true,
        likeCount: 256,
        onLike: {},
        onShare: {}
    )
    .padding()
    .background(Color(.systemBackground))
}

#Preview("长标题测试") {
    ContentInfoView(
        title: "这是一个非常长的标题，用来测试多行文本的显示效果和布局适应性，确保在各种设备上都能正常显示",
        content: "详细的内容，包含了很多有趣的信息和细节，让读者能够更好地了解这个地方的特色和魅力。这里有丰富的历史文化底蕴，独特的地理环境，以及热情好客的当地人民。",
        location: "某个很长的地名用来测试布局适应性",
        topic: Topic(id: "3", name: "文化体验", order: 3),
        isLiked: false,
        likeCount: 0,
        onLike: {},
        onShare: {}
    )
    .padding()
    .background(Color(.systemBackground))
}
