import SwiftUI

// MARK: - InteractionButtonStyle

/// 交互按钮样式 - 提供点击效果
/// 
/// 为发现详情页的交互按钮提供统一的点击反馈效果，
/// 包括缩放和透明度变化动画。
struct InteractionButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.spring(response: 0.2, dampingFraction: 0.8, blendDuration: 0), value: configuration.isPressed)
    }
}
