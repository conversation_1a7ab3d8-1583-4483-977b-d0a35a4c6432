import SwiftUI

/// 全屏图片预览器
/// 支持放大缩小、拖动查看、图片切换等功能
struct FullScreenImageViewerView: View {
    let imageUrls: [String]
    @Binding var currentPage: Int
    @Binding var isPresented: Bool

    // 自动滚动控制回调
    let onZoomChanged: (Bool) -> Void

    // MARK: - 状态管理

    /// 当前图片的缩放比例
    @State private var currentScale: CGFloat = 1.0
    /// 当前图片的偏移量
    @State private var currentOffset: CGSize = .zero
    /// 是否正在拖拽
    @State private var isDragging = false
    /// 是否正在缩放
    @State private var isZooming = false
    /// 当前图片是否被放大
    @State private var isCurrentImageZoomed = false

    // MARK: - 常量定义

    /// 最小缩放比例
    private let minScale: CGFloat = 1.0
    /// 最大缩放比例
    private let maxScale: CGFloat = 3.0
    /// 双击缩放比例
    private let doubleTapScale: CGFloat = 2.0

    // MARK: - 主视图

    var body: some View {
        ZStack {
            // 黑色背景
            Color.black
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景关闭
                    if !isCurrentImageZoomed {
                        isPresented = false
                    }
                }

            mainContentView()

            if isCurrentImageZoomed {
                dragOverlayView()
            }

            controlButtonsView()
        }
    }

    // MARK: - 图片轮播视图
    @ViewBuilder
    private func mainContentView() -> some View {
        TabView(selection: $currentPage) {
            ForEach(0..<imageUrls.count, id: \.self) { index in
                imageView(for: index)
                    .tag(index)
            }
        }
        .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        .onChange(of: currentPage) { _, _ in
            // 切换图片时重置缩放状态
            resetImageTransform()
        }
    }

    // MARK: - 单个图片视图
    @ViewBuilder
    private func imageView(for index: Int) -> some View {
        GeometryReader { geometry in
            CachedAsyncImage(url: URL(string: imageUrls[index])) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .scaleEffect(currentScale)
                    .offset(currentOffset)
                    .onTapGesture(count: 2) {
                        // 双击缩放
                        handleDoubleTap()
                    }
                    .gesture(
                        SimultaneousGesture(
                            // 拖拽手势
                            DragGesture()
                                .onChanged { value in
                                    if currentScale > 1.0 {
                                        isDragging = true
                                        currentOffset = CGSize(
                                            width: currentOffset.width + value.translation.width / currentScale,
                                            height: currentOffset.height + value.translation.height / currentScale
                                        )
                                    }
                                }
                                .onEnded { _ in
                                    isDragging = false
                                    // 边界检查和回弹
                                    withAnimation(.spring()) {
                                        currentOffset = boundOffset(currentOffset, in: geometry.size)
                                    }
                                },
                            // 缩放手势
                            MagnificationGesture()
                                .onChanged { value in
                                    isZooming = true
                                    let newScale = max(minScale, min(maxScale, value))
                                    currentScale = newScale
                                    updateZoomState()
                                }
                                .onEnded { _ in
                                    isZooming = false
                                    // 缩放边界检查
                                    withAnimation(.spring()) {
                                        if currentScale < minScale {
                                            currentScale = minScale
                                            currentOffset = .zero
                                        } else if currentScale > maxScale {
                                            currentScale = maxScale
                                        }
                                        currentOffset = boundOffset(currentOffset, in: geometry.size)
                                        updateZoomState()
                                    }
                                }
                        )
                    )
            } placeholder: {
                ProgressView()
                    .tint(.white)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } errorView: {
                VStack {
                    Image(systemName: "photo")
                        .font(.system(size: 48))
                        .foregroundColor(.white.opacity(0.6))
                    Text("图片加载失败")
                        .foregroundColor(.white.opacity(0.8))
                        .font(.caption)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
    }

    // MARK: - 拖拽覆盖层
    @ViewBuilder
    private func dragOverlayView() -> some View {
        Color.clear
            .contentShape(Rectangle())
            .gesture(
                DragGesture()
                    .onChanged { value in
                        // 只有在放大状态下才允许拖拽
                        if currentScale > 1.0 {
                            isDragging = true
                            currentOffset = CGSize(
                                width: currentOffset.width + value.translation.width / currentScale,
                                height: currentOffset.height + value.translation.height / currentScale
                            )
                        }
                    }
                    .onEnded { _ in
                        isDragging = false
                        withAnimation(.spring()) {
                            // 这里需要获取当前屏幕尺寸进行边界检查
                            // 暂时使用简单的边界限制
                            let maxOffset: CGFloat = 100
                            currentOffset = CGSize(
                                width: max(-maxOffset, min(maxOffset, currentOffset.width)),
                                height: max(-maxOffset, min(maxOffset, currentOffset.height))
                            )
                        }
                    }
            )
    }

    // MARK: - 控制按钮层
    @ViewBuilder
    private func controlButtonsView() -> some View {
        ZStack {
            // 顶部控制栏
            VStack {
                HStack {
                    // 关闭按钮
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                            .background(Color.black.opacity(0.5))
                            .clipShape(Circle())
                    }

                    Spacer()

                    // 重置按钮（放大时显示）
                    if isCurrentImageZoomed {
                        Button(action: resetImageTransform) {
                            Image(systemName: "arrow.down.right.and.arrow.up.left")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(width: 44, height: 44)
                                .background(Color.black.opacity(0.5))
                                .clipShape(Circle())
                        }
                        .transition(.scale(scale: 0.8).combined(with: .opacity).combined(with: .move(edge: .trailing)))
                    } else {
                        // 占位空间保持布局一致
                        Color.clear
                            .frame(width: 44, height: 44)
                    }
                }
                .padding(.horizontal, 20)

                Spacer()
            }

            // 右下角页面指示器
            if imageUrls.count > 1 {
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        HStack(spacing: 6) {
                            ForEach(0..<imageUrls.count, id: \.self) { index in
                                Circle()
                                    .fill(index == currentPage ? Color.white : Color.white.opacity(0.4))
                                    .frame(width: 8, height: 8)
                                    .scaleEffect(index == currentPage ? 1.2 : 1.0)
                                    .animation(.spring(response: 0.25, dampingFraction: 0.8, blendDuration: 0.05), value: currentPage)
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(16)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
                }
            }
        }
    }

    // MARK: - 辅助方法

    /// 处理双击缩放
    private func handleDoubleTap() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            if currentScale > 1.0 {
                // 当前已放大，重置到原始大小
                resetImageTransform()
            } else {
                // 当前是原始大小，放大到双击缩放比例
                currentScale = doubleTapScale
                currentOffset = .zero
                updateZoomState()
            }
        }
    }

    /// 重置图片变换状态
    private func resetImageTransform() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            currentScale = minScale
            currentOffset = .zero
            updateZoomState()
        }
    }

    /// 更新缩放状态
    private func updateZoomState() {
        let newZoomState = currentScale > 1.0
        if newZoomState != isCurrentImageZoomed {
            isCurrentImageZoomed = newZoomState
            onZoomChanged(newZoomState)
        }
    }

    /// 计算边界限制的偏移量
    private func boundOffset(_ offset: CGSize, in size: CGSize) -> CGSize {
        let maxOffsetX = max(0, (size.width * (currentScale - 1)) / 2)
        let maxOffsetY = max(0, (size.height * (currentScale - 1)) / 2)

        return CGSize(
            width: max(-maxOffsetX, min(maxOffsetX, offset.width)),
            height: max(-maxOffsetY, min(maxOffsetY, offset.height))
        )
    }
}

// MARK: - 预览
#Preview {
    FullScreenImageViewerView(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            "https://images.unsplash.com/photo-1469474968028-56623f02e42e"
        ],
        currentPage: .constant(0),
        isPresented: .constant(true),
        onZoomChanged: { _ in }
    )
}
