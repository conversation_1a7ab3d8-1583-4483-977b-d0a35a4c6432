import SwiftUI

/// 增强版全屏图片查看器
///
/// **核心特性:**
/// - 自然的图片缩放和拖拽体验
/// - 流畅的左右切换功能
/// - 与详情页一致的指示器样式
/// - 优雅的动画效果和手势处理
/// - 高效的内存管理和性能优化
///
/// **技术实现:**
/// - 使用ScrollView分页模式实现页面切换
/// - 分离缩放拖拽和页面切换的手势处理
/// - 精确的边界计算和回弹动画
/// - 组件化设计，职责清晰
struct EnhancedFullScreenImageViewer: View {

    // MARK: - Properties

    let imageUrls: [String]
    @Binding var currentPage: Int
    @Binding var isPresented: Bool
    let onZoomChanged: (Bool) -> Void

    // MARK: - View State

    /// 查看器状态
    private enum ViewerState {
        case normal      // 正常查看，可切换页面
        case zoomed      // 缩放状态，可拖拽图片
    }

    @State private var viewerState: ViewerState = .normal
    @State private var scrollPosition: Int? = nil

    // MARK: - Constants

    private let minScale: CGFloat = 1.0
    private let maxScale: CGFloat = 3.0
    private let doubleTapScale: CGFloat = 2.0

    // MARK: - Body

    var body: some View {
        ZStack {
            // 黑色背景
            Color.black
                .ignoresSafeArea()
                .onTapGesture {
                    if viewerState == .normal {
                        withAnimation(.easeOut(duration: 0.3)) {
                            isPresented = false
                        }
                    }
                }

            // 主要内容区域
            imageScrollView

            // 覆盖层控件
            overlayControls
        }
        .statusBarHidden()
    }

    // MARK: - Image Scroll View

    private var imageScrollView: some View {
        GeometryReader { geometry in
            ScrollViewReader { proxy in
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyHStack(spacing: 0) {
                        ForEach(0..<imageUrls.count, id: \.self) { index in
                            ZoomableImageView(
                                imageUrl: imageUrls[index],
                                isCurrentPage: index == currentPage,
                                containerSize: geometry.size,
                                onZoomChanged: { isZoomed in
                                    viewerState = isZoomed ? .zoomed : .normal
                                    onZoomChanged(isZoomed)
                                },
                                onResetZoom: {
                                    // 重置缩放的回调
                                }
                            )
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .id(index)
                        }
                    }
                }
                .scrollTargetBehavior(.paging)
                .scrollDisabled(viewerState == .zoomed) // 只在缩放时禁用
                .scrollPosition(id: $scrollPosition)
                .onAppear {
                    // 初始化滚动位置
                    scrollPosition = currentPage
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        proxy.scrollTo(currentPage, anchor: .center)
                    }
                }
                .onChange(of: scrollPosition) { _, newValue in
                    // 滚动位置变化时更新当前页面
                    if let newValue = newValue, newValue != currentPage {
                        currentPage = newValue
                    }
                }
                .onChange(of: currentPage) { oldValue, newValue in
                    // 当前页面变化时更新滚动位置
                    if newValue != scrollPosition {
                        scrollPosition = newValue
                        withAnimation(.easeInOut(duration: 0.3)) {
                            proxy.scrollTo(newValue, anchor: .center)
                        }
                    }
                }
            }
        }
    }

    // MARK: - Overlay Controls

    private var overlayControls: some View {
        VStack {
            // 顶部控制栏
            HStack {
                closeButton
                Spacer()

                // 重置缩放按钮（仅在缩放状态下显示）
                if viewerState == .zoomed {
                    resetZoomButton
                        .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)

            Spacer()

            // 底部指示器
            if imageUrls.count > 1 {
                PageIndicatorView(
                    totalPages: imageUrls.count,
                    currentPage: currentPage
                )
                .padding(.bottom, 40)
            }
        }
    }

    /// 关闭按钮
    private var closeButton: some View {
        Button(action: {
            withAnimation(.easeOut(duration: 0.3)) {
                isPresented = false
            }
        }) {
            Image(systemName: "xmark")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 44, height: 44)
                .background(Color.black.opacity(0.3))
                .clipShape(Circle())
        }
    }

    /// 重置缩放按钮
    private var resetZoomButton: some View {
        Button(action: {
            // 通知当前页面的ZoomableImageView重置缩放
            NotificationCenter.default.post(name: .resetZoom, object: nil)
        }) {
            Image(systemName: "arrow.down.right.and.arrow.up.left")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 44, height: 44)
                .background(Color.black.opacity(0.3))
                .clipShape(Circle())
        }
    }

}

// MARK: - ZoomableImageView

/// 可缩放拖拽的图片视图组件
struct ZoomableImageView: View {

    // MARK: - Properties

    let imageUrl: String
    let isCurrentPage: Bool
    let containerSize: CGSize
    let onZoomChanged: (Bool) -> Void
    let onResetZoom: () -> Void

    // MARK: - Gesture State

    @GestureState private var magnification: CGFloat = 1.0
    @GestureState private var panOffset: CGSize = .zero
    @State private var baseScale: CGFloat = 1.0
    @State private var baseOffset: CGSize = .zero

    // MARK: - Constants

    private let minScale: CGFloat = 1.0
    private let maxScale: CGFloat = 3.0
    private let doubleTapScale: CGFloat = 2.0

    // MARK: - Computed Properties

    private var currentScale: CGFloat {
        baseScale * magnification
    }

    private var currentOffset: CGSize {
        CGSize(
            width: baseOffset.width + panOffset.width,
            height: baseOffset.height + panOffset.height
        )
    }

    // MARK: - Body

    var body: some View {
        GeometryReader { geometry in
            CachedAsyncImage(url: URL(string: imageUrl)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .scaleEffect(currentScale)
                    .offset(currentOffset)
                    .onTapGesture(count: 2) {
                        handleDoubleTap()
                    }
                    .gesture(
                        // 缩放手势
                        MagnificationGesture()
                            .updating($magnification) { value, state, _ in
                                state = value
                            }
                            .onEnded { value in
                                let newScale = baseScale * value
                                baseScale = max(minScale, min(maxScale, newScale))

                                // 如果缩放到最小值，重置偏移
                                if baseScale <= minScale {
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        baseOffset = .zero
                                    }
                                } else {
                                    // 检查并修正偏移边界
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        baseOffset = boundOffset(baseOffset, scale: baseScale, containerSize: containerSize)
                                    }
                                }

                                updateZoomState()
                            }
                    )
                    .if(baseScale > minScale) { view in
                        // 只有在缩放状态下才添加拖拽手势
                        view.simultaneousGesture(
                            DragGesture(minimumDistance: 1)
                                .updating($panOffset) { value, state, _ in
                                    state = value.translation
                                }
                                .onEnded { value in
                                    baseOffset = CGSize(
                                        width: baseOffset.width + value.translation.width,
                                        height: baseOffset.height + value.translation.height
                                    )

                                    // 边界检查和回弹
                                    withAnimation(.easeOut(duration: 0.3)) {
                                        baseOffset = boundOffset(baseOffset, scale: baseScale, containerSize: containerSize)
                                    }
                                }
                        )
                    }
            } placeholder: {
                ProgressView()
                    .tint(.white)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } errorView: {
                VStack(spacing: 8) {
                    Image(systemName: "photo")
                        .font(.system(size: 48))
                        .foregroundColor(.white.opacity(0.6))
                    Text("图片加载失败")
                        .foregroundColor(.white.opacity(0.8))
                        .font(.caption)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .onChange(of: isCurrentPage) { _, newValue in
            // 切换到其他页面时重置缩放状态
            if !newValue {
                resetZoom()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .resetZoom)) { _ in
            // 接收重置缩放通知
            if isCurrentPage {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    resetZoom()
                }
            }
        }
    }

    // MARK: - Private Methods

    /// 处理双击缩放
    private func handleDoubleTap() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            if baseScale > minScale {
                // 当前已放大，重置到原始大小
                resetZoom()
            } else {
                // 当前是原始大小，放大到双击缩放比例
                baseScale = doubleTapScale
                baseOffset = .zero
                updateZoomState()
            }
        }
    }

    /// 重置缩放状态
    private func resetZoom() {
        baseScale = minScale
        baseOffset = .zero
        updateZoomState()
    }

    /// 更新缩放状态
    private func updateZoomState() {
        let isZoomed = baseScale > minScale
        onZoomChanged(isZoomed)
    }

    /// 计算边界限制的偏移量
    private func boundOffset(_ offset: CGSize, scale: CGFloat, containerSize: CGSize) -> CGSize {
        // 假设图片是适应容器的，计算缩放后的图片尺寸
        let scaledImageWidth = containerSize.width * scale
        let scaledImageHeight = containerSize.height * scale

        // 计算最大允许的偏移量
        let maxOffsetX = max(0, (scaledImageWidth - containerSize.width) / 2)
        let maxOffsetY = max(0, (scaledImageHeight - containerSize.height) / 2)

        return CGSize(
            width: max(-maxOffsetX, min(maxOffsetX, offset.width)),
            height: max(-maxOffsetY, min(maxOffsetY, offset.height))
        )
    }
}

// MARK: - Notification Extension

extension Notification.Name {
    static let resetZoom = Notification.Name("resetZoom")
}

// MARK: - Preview

#Preview {
    EnhancedFullScreenImageViewer(
        imageUrls: [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4",
            "https://images.unsplash.com/photo-1469474968028-56623f02e42e",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e"
        ],
        currentPage: .constant(0),
        isPresented: .constant(true),
        onZoomChanged: { _ in }
    )
}
