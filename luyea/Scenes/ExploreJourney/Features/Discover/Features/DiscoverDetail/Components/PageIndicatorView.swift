import SwiftUI

// MARK: - PageIndicatorView

/// 页面指示器组件
///
/// **功能概述:**
/// - 显示当前页面在总页数中的位置
/// - 支持动态宽度变化，当前页指示器更宽
/// - 使用iOS 18的ultraThinMaterial效果
/// - 自动隐藏单页内容的指示器
///
/// **设计特色:**
/// - 胶囊式现代化设计
/// - 流畅的弹簧动画过渡
/// - 半透明毛玻璃背景效果
/// - 白色指示点，当前页更宽更明显
struct PageIndicatorView: View {

    // MARK: - Properties

    /// 总页数
    let totalPages: Int
    /// 当前页索引（从0开始）
    let currentPage: Int

    // MARK: - Body

    var body: some View {
        // 只有多页时才显示指示器
        if totalPages > 1 {
            indicatorContent
        }
    }

    /// 指示器内容
    private var indicatorContent: some View {
        HStack(spacing: 4) {
            ForEach(0..<totalPages, id: \.self) { index in
                indicatorDot(for: index)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(indicatorBackground)
    }

    /// 单个指示器点
    /// - Parameter index: 页面索引
    /// - Returns: 指示器点视图
    private func indicatorDot(for index: Int) -> some View {
        RoundedRectangle(cornerRadius: 4)
            .fill(index == currentPage ? Color.white : Color.white.opacity(0.5))
            .frame(
                width: index == currentPage ? 20 : 8, // 当前页更宽
                height: 8
            )
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: currentPage)
    }

    /// 指示器背景（与其他覆层按钮样式一致）
    private var indicatorBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color.black.opacity(0.3)) // 与作者头像等覆层按钮样式一致
    }
}

// MARK: - Preview

#Preview("多页指示器") {
    ZStack {
        Color.blue
            .ignoresSafeArea()

        VStack(spacing: 20) {
            PageIndicatorView(totalPages: 3, currentPage: 0)
            PageIndicatorView(totalPages: 3, currentPage: 1)
            PageIndicatorView(totalPages: 3, currentPage: 2)
        }
    }
}

#Preview("单页隐藏测试") {
    ZStack {
        Color.blue
            .ignoresSafeArea()

        VStack(spacing: 20) {
            Text("单页时不显示指示器:")
                .foregroundColor(.white)
            PageIndicatorView(totalPages: 1, currentPage: 0)

            Text("多页时显示指示器:")
                .foregroundColor(.white)
            PageIndicatorView(totalPages: 5, currentPage: 2)
        }
    }
}
