import SwiftUI

/// 发现详情页主视图
///
/// 简化的包装器，直接使用DiscoverDetailContentView
struct DiscoverDetailView: View {
    let navigationParams: DiscoverDetailModels.NavigationParams

    var body: some View {
        NavigationStack {
            DiscoverDetailContentView(navigationParams: navigationParams)
        }
    }
}

// MARK: - Preview

#Preview {
    NavigationStack {
        DiscoverDetailView(
            navigationParams: DiscoverDetailModels.NavigationParams(
                id: "preview_001",
                previewData: DiscoverDetailModels.PreviewData(
                    title: "示例标题",
                    imageUrls: ["https://c-ssl.dtstatic.com/uploads/blog/202408/14/9WSP7q6eh8wwMP6.thumb.1000_0.jpg"],
                    authorName: "用户名",
                    authorAvatar: "https://img1.baidu.com/it/u=1747081318,2650263390&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
                )
            )
        )
    }
}
