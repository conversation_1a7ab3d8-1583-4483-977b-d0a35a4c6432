import Foundation
import Combine

/// 发现详情页服务协议
protocol DiscoverDetailServiceProtocol {
    /// 获取详情内容
    func fetchDetailContent(id: String) async throws -> DiscoverDetailModels.DetailContent

    /// 获取评论列表
    func fetchComments(contentId: String, page: Int, pageSize: Int) async throws -> PaginationResponse<DiscoverDetailModels.Comment>

    /// 获取评论回复列表
    func fetchCommentReplies(contentId: String, commentId: String, page: Int, pageSize: Int) async throws -> PaginationResponse<DiscoverDetailModels.CommentReply>

    /// 点赞/取消点赞
    func toggleLike(contentId: String, isLiked: Bool) async throws -> Bool

    /// 收藏/取消收藏
    func toggleCollect(contentId: String, isCollected: Bool) async throws -> Bool
    
    /// 关注/取消关注作者
    func toggleFollowAuthor(authorId: String, isFollowing: Bool) async throws -> Bool
    
    /// Fork旅程路线
    func forkJourney(journeyId: String) async throws -> String // 返回新的旅程ID
    
    /// 提交评论
    func submitComment(contentId: String, content: String, parentCommentId: String?) async throws -> DiscoverDetailModels.Comment

    /// 分享内容
    func shareContent(contentId: String, platform: String?) async throws -> String // 返回分享链接
}

/// 发现详情页服务实现
final class DiscoverDetailService: DiscoverDetailServiceProtocol {
    
    // MARK: - Dependencies
    
    private let networkService: NetworkServiceProtocol
    
    // MARK: - Cache Properties
    
    /// 详情内容缓存
    private var contentCache: [String: DiscoverDetailModels.DetailContent] = [:]
    
    /// 评论缓存
    private var commentsCache: [String: PaginationResponse<DiscoverDetailModels.Comment>] = [:]

    /// 回复缓存
    private var repliesCache: [String: PaginationResponse<DiscoverDetailModels.CommentReply>] = [:]

    /// 缓存有效期（5分钟）
    private let cacheValidDuration: TimeInterval = 300
    
    /// 最后更新时间
    private var lastUpdateTimes: [String: Date] = [:]
    
    // MARK: - Initialization
    
    init(networkService: NetworkServiceProtocol = NetworkService.shared) {
        self.networkService = networkService
    }
    
    // MARK: - Public Methods
    
    /// 获取详情内容
    func fetchDetailContent(id: String) async throws -> DiscoverDetailModels.DetailContent {
        let cacheKey = "content_\(id)"

        // 检查缓存
        if let cachedContent = getCachedContent(for: cacheKey) {
            Log.debug("🎯 [DiscoverDetailService] 使用缓存的详情内容: \(id)")
            return cachedContent
        }

        // 构建API请求
        let api = APIRequest.get("/api/v1/discover/\(id)/detail")
        
        Log.info("🌐 [DiscoverDetailService] 获取详情内容: \(id)")
        
        do {
            let content: DiscoverDetailModels.DetailContent = try await networkService.request(api)
            
            // 缓存结果
            setCachedContent(content, for: cacheKey)
            
            Log.success("✅ [DiscoverDetailService] 详情内容获取成功: \(content.title)")
            return content
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 详情内容获取失败: \(error)")
            throw error
        }
    }
    
    /// 获取评论列表
    func fetchComments(contentId: String, page: Int = 1, pageSize: Int = 20) async throws -> PaginationResponse<DiscoverDetailModels.Comment> {
        let cacheKey = "comments_\(contentId)_\(page)_\(pageSize)"
        
        // 检查缓存
        if let cachedComments = getCachedComments(for: cacheKey) {
            Log.debug("🎯 [DiscoverDetailService] 使用缓存的评论数据: \(contentId)")
            return cachedComments
        }
        
        // 构建API请求
        let api = APIRequest.get("/api/v1/discover/\(contentId)/comments")
            .query([
                "page": "\(page)",
                "pageSize": "\(pageSize)"
            ])
        
        Log.info("🌐 [DiscoverDetailService] 获取评论列表: \(contentId), 页码: \(page)")
        
        do {
            let response: PaginationResponse<DiscoverDetailModels.Comment> = try await networkService.request(api)
            
            // 缓存结果
            setCachedComments(response, for: cacheKey)
            
            Log.success("✅ [DiscoverDetailService] 评论列表获取成功: \(response.items.count)条评论")
            return response
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 评论列表获取失败: \(error)")
            throw error
        }
    }

    /// 获取评论回复列表
    func fetchCommentReplies(contentId: String, commentId: String, page: Int = 1, pageSize: Int = 20) async throws -> PaginationResponse<DiscoverDetailModels.CommentReply> {
        let cacheKey = "replies_\(commentId)_\(page)_\(pageSize)"

        // 检查缓存
        if let cachedReplies = getCachedReplies(for: cacheKey) {
            Log.debug("🎯 [DiscoverDetailService] 使用缓存的回复数据: \(commentId)")
            return cachedReplies
        }

        // 构建API请求
        let api = APIRequest.get("/api/v1/discover/\(contentId)/comments/1/replies")
            .query([
                "page": "\(page)",
                "pageSize": "\(pageSize)"
            ])

        Log.info("🌐 [DiscoverDetailService] 获取评论回复: \(commentId), 页码: \(page)")

        do {
            let response: PaginationResponse<DiscoverDetailModels.CommentReply> = try await networkService.request(api)

            // 缓存结果
            setCachedReplies(response, for: cacheKey)

            Log.success("✅ [DiscoverDetailService] 评论回复获取成功: \(response.items.count)条回复")
            return response

        } catch {
            Log.error("❌ [DiscoverDetailService] 评论回复获取失败: \(error)")
            throw error
        }
    }

    /// 点赞/取消点赞
    func toggleLike(contentId: String, isLiked: Bool) async throws -> Bool {
        let api = isLiked ?
            APIRequest.delete("/api/v1/discover/\(contentId)/like") :
            APIRequest.post("/api/v1/discover/\(contentId)/like")
        
        Log.info("🌐 [DiscoverDetailService] \(isLiked ? "取消点赞" : "点赞"): \(contentId)")
        
        do {
            let response: [String: Bool] = try await networkService.request(api)
            let newLikedState = response["isLiked"] ?? !isLiked
            
            // 清除相关缓存
            clearContentCache(for: contentId)
            
            Log.success("✅ [DiscoverDetailService] 点赞状态更新成功: \(newLikedState)")
            return newLikedState
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 点赞操作失败: \(error)")
            throw error
        }
    }
    
    /// 收藏/取消收藏
    func toggleCollect(contentId: String, isCollected: Bool) async throws -> Bool {
        let api = isCollected ?
            APIRequest.delete("/api/v1/discover/\(contentId)/collect") :
            APIRequest.post("/api/v1/discover/\(contentId)/collect")
        
        Log.info("🌐 [DiscoverDetailService] \(isCollected ? "取消收藏" : "收藏"): \(contentId)")
        
        do {
            let response: [String: Bool] = try await networkService.request(api)
            let newCollectedState = response["isCollected"] ?? !isCollected
            
            // 清除相关缓存
            clearContentCache(for: contentId)
            
            Log.success("✅ [DiscoverDetailService] 收藏状态更新成功: \(newCollectedState)")
            return newCollectedState
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 收藏操作失败: \(error)")
            throw error
        }
    }
    
    /// 关注/取消关注作者
    func toggleFollowAuthor(authorId: String, isFollowing: Bool) async throws -> Bool {
        let api = isFollowing ?
            APIRequest.delete("/api/v1/users/\(authorId)/follow") :
            APIRequest.post("/api/v1/users/\(authorId)/follow")
        
        Log.info("🌐 [DiscoverDetailService] \(isFollowing ? "取消关注" : "关注")作者: \(authorId)")
        
        do {
            let response: [String: Bool] = try await networkService.request(api)
            let newFollowingState = response["isFollowing"] ?? !isFollowing
            
            Log.success("✅ [DiscoverDetailService] 关注状态更新成功: \(newFollowingState)")
            return newFollowingState
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 关注操作失败: \(error)")
            throw error
        }
    }
    
    /// Fork旅程路线
    func forkJourney(journeyId: String) async throws -> String {
        let api = APIRequest.post("/api/v1/journeys/\(journeyId)/fork")
        
        Log.info("🌐 [DiscoverDetailService] Fork旅程路线: \(journeyId)")
        
        do {
            let response: [String: String] = try await networkService.request(api)
            guard let newJourneyId = response["newJourneyId"] else {
                throw NetworkError.invalidData
            }
            
            Log.success("✅ [DiscoverDetailService] 旅程Fork成功: \(newJourneyId)")
            return newJourneyId
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 旅程Fork失败: \(error)")
            throw error
        }
    }
    
    /// 提交评论
    func submitComment(contentId: String, content: String, parentCommentId: String? = nil) async throws -> DiscoverDetailModels.Comment {
        var body: [String: Any] = ["content": content]
        if let parentId = parentCommentId {
            body["parentCommentId"] = parentId
        }
        
        // 将body转换为JSON Data
        let bodyData = try JSONSerialization.data(withJSONObject: body)

        let api = APIRequest.post("/api/v1/discover/\(contentId)/comments")
            .body(bodyData)
        
        Log.info("🌐 [DiscoverDetailService] 提交评论: \(contentId)")
        
        do {
            let comment: DiscoverDetailModels.Comment = try await networkService.request(api)
            
            // 清除评论缓存
            clearCommentsCache(for: contentId)
            
            Log.success("✅ [DiscoverDetailService] 评论提交成功: \(comment.id)")
            return comment
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 评论提交失败: \(error)")
            throw error
        }
    }
    
    /// 分享内容
    func shareContent(contentId: String, platform: String? = nil) async throws -> String {
        var query: [String: String] = [:]
        if let platform = platform {
            query["platform"] = platform
        }
        
        let api = APIRequest.post("/api/v1/discover/\(contentId)/share")
            .query(query)
        
        Log.info("🌐 [DiscoverDetailService] 生成分享链接: \(contentId)")
        
        do {
            let response: [String: String] = try await networkService.request(api)
            guard let shareUrl = response["shareUrl"] else {
                throw NetworkError.invalidData
            }
            
            Log.success("✅ [DiscoverDetailService] 分享链接生成成功")
            return shareUrl
            
        } catch {
            Log.error("❌ [DiscoverDetailService] 分享链接生成失败: \(error)")
            throw error
        }
    }
}

// MARK: - Private Cache Methods

private extension DiscoverDetailService {
    
    /// 获取缓存的内容
    func getCachedContent(for key: String) -> DiscoverDetailModels.DetailContent? {
        guard let content = contentCache[key],
              let lastUpdate = lastUpdateTimes[key],
              Date().timeIntervalSince(lastUpdate) < cacheValidDuration else {
            return nil
        }
        return content
    }
    
    /// 设置缓存内容
    func setCachedContent(_ content: DiscoverDetailModels.DetailContent, for key: String) {
        contentCache[key] = content
        lastUpdateTimes[key] = Date()
    }
    
    /// 获取缓存的评论
    func getCachedComments(for key: String) -> PaginationResponse<DiscoverDetailModels.Comment>? {
        guard let comments = commentsCache[key],
              let lastUpdate = lastUpdateTimes[key],
              Date().timeIntervalSince(lastUpdate) < cacheValidDuration else {
            return nil
        }
        return comments
    }
    
    /// 设置缓存评论
    func setCachedComments(_ comments: PaginationResponse<DiscoverDetailModels.Comment>, for key: String) {
        commentsCache[key] = comments
        lastUpdateTimes[key] = Date()
    }
    
    /// 清除内容缓存
    func clearContentCache(for contentId: String) {
        let keysToRemove = contentCache.keys.filter { $0.contains(contentId) }
        keysToRemove.forEach { key in
            contentCache.removeValue(forKey: key)
            lastUpdateTimes.removeValue(forKey: key)
        }
    }

    /// 清除评论缓存
    func clearCommentsCache(for contentId: String) {
        let keysToRemove = commentsCache.keys.filter { $0.contains(contentId) }
        keysToRemove.forEach { key in
            commentsCache.removeValue(forKey: key)
            lastUpdateTimes.removeValue(forKey: key)
        }
    }

    /// 获取缓存的回复
    func getCachedReplies(for key: String) -> PaginationResponse<DiscoverDetailModels.CommentReply>? {
        guard let replies = repliesCache[key],
              let lastUpdate = lastUpdateTimes[key],
              Date().timeIntervalSince(lastUpdate) < cacheValidDuration else {
            return nil
        }
        return replies
    }

    /// 设置缓存回复
    func setCachedReplies(_ replies: PaginationResponse<DiscoverDetailModels.CommentReply>, for key: String) {
        repliesCache[key] = replies
        lastUpdateTimes[key] = Date()
    }

    /// 清除回复缓存
    func clearRepliesCache(for commentId: String) {
        let keysToRemove = repliesCache.keys.filter { $0.contains(commentId) }
        keysToRemove.forEach { key in
            repliesCache.removeValue(forKey: key)
            lastUpdateTimes.removeValue(forKey: key)
        }
    }
}
