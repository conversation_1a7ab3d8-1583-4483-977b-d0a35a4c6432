import Foundation
import SwiftUI

/// 发现详情模块常量定义
enum DiscoverDetailConstants {
    
    // MARK: - 核心文本常量
    enum Text {
        static let loadingError = "加载失败"
        static let retryAction = "重试"
        static let shareAction = "分享"
        static let likeAction = "喜欢"
        static let commentPlaceholder = "写下你的想法..."
        static let submitComment = "发布"
        static let viewJourney = "查看行程"
        static let forkJourney = "复制行程"
        static let relatedRoute = "相关路线"
        static let noComments = "暂无评论"
        static let loginToComment = "登录后可评论"
    }
    
    // MARK: - 核心布局常量
    enum Layout {
        static let navigationBarHeight: CGFloat = 40
        static let cardCornerRadius: CGFloat = 16
        static let imageCarouselHeight: CGFloat = 300
        static let authorAvatarSize: CGFloat = 40
        static let commentAvatarSize: CGFloat = 32
        static let maxCommentLength = 500
        static let maxDisplayedComments = 10
    }
    
    // MARK: - 核心动画常量
    enum Animation {
        static let navigationTransition: SwiftUI.Animation = .easeInOut(duration: 0.3)
        static let likeButtonScale: SwiftUI.Animation = .spring(response: 0.3, dampingFraction: 0.6)
        static let imageCarouselTransition: SwiftUI.Animation = .easeInOut(duration: 0.4)
        static let cardAppear: SwiftUI.Animation = .easeOut(duration: 0.4)
        static let buttonPress: SwiftUI.Animation = .easeInOut(duration: 0.2)
        static let contentTransition: SwiftUI.Animation = .spring(response: 0.5, dampingFraction: 0.8)
    }
    
    // MARK: - 状态颜色
    enum StatusColors {
        static let liked = Color.red
        static let unliked = Color.gray
        static let commentValid = Color.blue
        static let commentInvalid = Color.gray
        static let topicTag = Color.blue
        static let locationTag = Color.orange
        static let cardBackground = Color(.systemBackground)
        static let sectionBackground = Color(.systemGroupedBackground)
    }
    
    // MARK: - 业务配置
    enum Config {
        static let imageLoadTimeout: TimeInterval = 10
        static let commentSubmitDebounce: TimeInterval = 0.5
        static let autoRefreshInterval: TimeInterval = 60
        static let maxImageCacheSize: Int = 50
    }
    
    // MARK: - API配置
    enum API {
        static let defaultPageSize = 20
        static let maxRetryCount = 3
        static let requestTimeout: TimeInterval = 30
    }
}
