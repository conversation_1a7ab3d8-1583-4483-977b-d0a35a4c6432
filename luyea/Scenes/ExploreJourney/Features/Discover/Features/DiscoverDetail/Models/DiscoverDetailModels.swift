import Foundation

/// 发现详情页数据模型命名空间
enum DiscoverDetailModels {
    
    // MARK: - Navigation Parameters
    
    /// 详情页导航参数
    /// 从瀑布流页面传递到详情页的最小必要参数
    struct NavigationParams: Identifiable, Hashable {
        let id: String // 内容ID（API参数用字符串）
        let previewData: PreviewData? // 预览数据（用于快速显示）

        init(id: String, previewData: PreviewData? = nil) {
            self.id = id
            self.previewData = previewData
        }

        static func == (lhs: NavigationParams, rhs: NavigationParams) -> Bool {
            lhs.id == rhs.id
        }

        func hash(into hasher: inout Hasher) {
            hasher.combine(id)
        }
    }
    
    /// 预览数据（用于快速显示，避免白屏）
    struct PreviewData: Codable {
        let title: String
        let imageUrls: [String]
        let authorName: String
        let authorAvatar: String?

        init(title: String, imageUrls: [String] = [], authorName: String, authorAvatar: String? = nil) {
            self.title = title
            self.imageUrls = imageUrls
            self.authorName = authorName
            self.authorAvatar = authorAvatar
        }

        /// 兼容性属性：获取封面图片（第一张图片）
        var coverImage: String? {
            imageUrls.first
        }
    }
    
    // MARK: - Detail Content Models
    
    /// 详情页完整内容数据
    struct DetailContent: Identifiable, Codable, Equatable {
        let id: String
        let title: String
        let content: String
        let images: [MediaItem]
        let author: AuthorInfo
        let location: LocationInfo?
        let topic: Topic // 发现内容必然包含话题标签
        var stats: ContentStats
        let journey: JourneyRoute?
        let createdAt: Date
        let updatedAt: Date?

        // MARK: - 字段统一支持
        /// 顶级图片URL数组（与发现列表页保持一致）
        let imageUrls: [String]?
        /// 是否允许Fork（与发现列表页保持一致）
        let allowFork: Bool?

        /// 获取点赞数（统一使用stats.likeCount）
        var likeCount: Int {
            stats.likeCount
        }

        /// 获取图片URL数组（优先使用顶级字段）
        var topLevelImageUrls: [String] {
            imageUrls ?? images.map(\.url)
        }

        /// 是否包含旅程路线
        var hasJourney: Bool {
            journey != nil
        }
    }
    
    /// 媒体项目
    struct MediaItem: Identifiable, Codable, Equatable {
        let id: String
        let url: String
        let type: MediaType

        enum MediaType: String, Codable {
            case image = "image"
            case video = "video"
        }
    }
    
    /// 作者信息
    struct AuthorInfo: Codable, Equatable {
        let id: String
        let username: String
        let avatarUrl: String?
    }
    
    /// 位置信息
    struct LocationInfo: Codable, Equatable {
        let name: String
        let address: String?
        let coordinate: Coordinate?
        let city: String?
        let province: String?
        let country: String?
    }
    
    /// 内容统计
    struct ContentStats: Codable, Equatable {
        var viewCount: Int
        var likeCount: Int
        var commentCount: Int
        var shareCount: Int
        var forkCount: Int // 旅程被Fork的次数
        var collectCount: Int

        /// 用户交互状态
        var userInteraction: UserInteraction = UserInteraction()
    }
    
    /// 用户交互状态
    struct UserInteraction: Codable, Equatable {
        var isLiked: Bool = false
        var isCollected: Bool = false
        var isFollowingAuthor: Bool = false
    }
    
    // MARK: - Journey Route Models

    /// 坐标
    struct Coordinate: Codable, Equatable {
        let latitude: Double
        let longitude: Double
    }

    /// 旅程路线
    struct JourneyRoute: Identifiable, Codable, Equatable {
        let id: String
        let title: String
        let content: String?
        let duration: Int // 天数，如：3
        let destinations: [RouteDestination]
        let totalDistance: Double? // 总距离（公里）
        let estimatedCost: CostRange?
        let difficulty: RouteDifficulty
        let forkCount: Int? // Fork 数量
        let tags: [String]
        let highlights: [String] // 路线亮点
        let tips: [String] // 旅行贴士
        let bestSeasons: [String] // 最佳季节
        let createdAt: Date
        let updatedAt: Date?
    }
    
    /// 路线目的地
    struct RouteDestination: Identifiable, Codable, Equatable {
        let id: String
        let name: String
        let location: String
        let coordinate: Coordinate?
        let order: Int // 在路线中的顺序
        let stayDuration: String? // 停留时间，如："2小时"
        let content: String?
        let imageUrl: String?
        let attractions: [AttractionPoint] // 主要景点
        let activities: [String] // 推荐活动
        let tips: [String] // 目的地贴士
    }
    
    /// 景点点位
    struct AttractionPoint: Identifiable, Codable, Equatable {
        let id: String
        let name: String
        let type: String // 景点类型
        let content: String?
        let coordinate: Coordinate?
        let rating: Double?
        let visitDuration: String? // 建议游览时间
        let ticketPrice: String? // 门票价格
        let openingHours: String? // 开放时间
    }
    
    /// 费用范围
    struct CostRange: Codable, Equatable {
        let min: Double
        let max: Double
        let currency: String
        
        var displayText: String {
            if min == max {
                return "¥\(Int(min))"
            } else {
                return "¥\(Int(min))-\(Int(max))"
            }
        }
    }
    
    /// 路线难度等级
    enum RouteDifficulty: String, Codable, CaseIterable {
        case easy = "easy"
        case medium = "medium"
        case hard = "hard"
        
        var displayName: String {
            switch self {
            case .easy: return "轻松"
            case .medium: return "中等"
            case .hard: return "困难"
            }
        }
        
        var color: String {
            switch self {
            case .easy: return "green"
            case .medium: return "orange"
            case .hard: return "red"
            }
        }
        
        var icon: String {
            switch self {
            case .easy: return "leaf"
            case .medium: return "figure.walk"
            case .hard: return "mountain.2"
            }
        }
    }
    
    // MARK: - Comment Models
    
    /// 评论
    struct Comment: Identifiable, Codable {
        let id: String
        let content: String
        let author: CommentAuthor
        let createdAt: Date
        let likeCount: Int
        let isLiked: Bool
        let replies: [CommentReply]
        let replyCount: Int? // 总回复数量
        let hasMoreReplies: Bool? // 是否有更多回复
    }
    
    /// 评论作者
    struct CommentAuthor: Codable {
        let id: String
        let username: String
        let avatarUrl: String?
        let location: String? // 归属地
    }
    
    /// 评论回复
    struct CommentReply: Identifiable, Codable {
        let id: String
        let content: String
        let author: CommentAuthor
        let createdAt: Date
        let likeCount: Int
        let isLiked: Bool
        let replyToUser: CommentAuthor? // 回复的目标用户，如果为nil则是回复主评论
        let parentReplyId: String? // 父回复ID，用于嵌套回复
    }
}

// MARK: - Note: Coordinate Model
// 使用Core层统一的Coordinate模型，无需重复定义
