import SwiftUI

/// 发现详情页内容视图
///
/// 负责展示发现内容的详细信息，包括图片轮播、内容信息、
/// 相关旅程和评论等模块。
struct DiscoverDetailContentView: View {
    // MARK: - Properties
    let navigationParams: DiscoverDetailModels.NavigationParams
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel: DiscoverDetailViewModel

    /// 导航栏是否显示（用于控制返回按钮颜色和标题显示）
    @State private var isNavigationBarVisible = false

    /// 动态导航栏标题
    private var navigationTitle: String {
        isNavigationBarVisible ? viewModel.contentInfo.title : ""
    }

    // MARK: - Initialization
    init(navigationParams: DiscoverDetailModels.NavigationParams) {
        self.navigationParams = navigationParams
        self._viewModel = StateObject(wrappedValue: DiscoverDetailViewModel(navigationParams: navigationParams))
    }

    // MARK: - Body

    var body: some View {
        mainContentScrollView
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle(navigationTitle)
            .navigationBarBackButtonHidden(true)
            .enableSwipeBackGesture()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    customBackButton
                }
            }
            .hideTabBar()
            .fullScreenCover(isPresented: $viewModel.showFullScreenViewer) {
                fullScreenImageViewer
            }
            .sheet(isPresented: $viewModel.isAuthorProfilePresented) { authorProfileSheet }
            .sheet(isPresented: $viewModel.showJourneyDetail) { journeyDetailSheet }
            .onAppear { setupInitialState() }
    }

    // MARK: - Main Content Views

    private var mainContentScrollView: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 0) {
                imageCarouselSection
                    .withNavigationBarScrollTracking(
                        threshold: 270,
                        isVisible: $isNavigationBarVisible
                    )

                VStack(alignment: .leading, spacing: 24) {
                    contentInfoSection
                    journeyRouteSection
                    commentsSection
                }
                .padding(.top, 16)
                .padding(.horizontal, 20)
            }
            .padding(.bottom, 40)
        }
        .toolbarBackground(isNavigationBarVisible ? .visible : .hidden, for: .navigationBar)
        .ignoresSafeArea(edges: .top)
        .background(Color(.systemBackground))
    }


    // MARK: - Image Carousel Section

    /// 图片轮播区域，支持下拉拉伸效果
    private var imageCarouselSection: some View {
        GeometryReader { geometry in
            let offset = geometry.frame(in: .global).minY
            let baseHeight: CGFloat = 380

            // 计算拉伸高度和偏移量，实现下拉时的图片拉伸效果
            let stretchHeight = max(baseHeight, baseHeight + offset)
            let yOffset = offset > 0 ? -offset : 0

            let authorInfo = viewModel.authorInfo

            ImageCarouselSection(
                imageUrls: viewModel.imageUrls,
                currentPage: $viewModel.currentPage,
                isAutoScrolling: $viewModel.isAutoScrolling,
                authorAvatarUrl: authorInfo.avatarUrl,
                authorUsername: authorInfo.username,
                onImageTap: { viewModel.showFullScreenImageViewer() },
                onAuthorTap: { viewModel.showAuthorProfile() },
                onZoomChanged: { viewModel.handleZoomChanged($0) }
            )
            .frame(height: stretchHeight)
            .offset(y: yOffset)
            .clipped()
        }
        .frame(height: 380)
    }



    private var contentInfoSection: some View {
        let contentInfo = viewModel.contentInfo
        let stats = viewModel.stats
        return ContentInfoView(
            title: contentInfo.title,
            content: contentInfo.content,
            location: contentInfo.location ?? "",
            topic: viewModel.topic,
            isLiked: viewModel.isLiked,
            likeCount: stats.likeCount,
            onLike: { viewModel.handleLike() },
            onShare: { viewModel.handleShare() }
        )
    }

    @ViewBuilder
    private var journeyRouteSection: some View {
        if let journey = viewModel.journey {
            JourneyRouteCardView(
                journey: journey,
                onViewTap: { viewModel.showJourneyDetail = true },
                onForkTap: { print("复制行程功能待实现") }
            )
        }
    }

    // MARK: - Section Views

    private var commentsSection: some View {
        let stats = viewModel.stats
        return CommentsSection(
            comments: viewModel.comments,
            commentCount: stats.commentCount,
            commentText: $viewModel.commentText,
            onCommentSubmit: handleCommentSubmission,
            isCommentTextValid: isCommentTextValid,
            isLoadingComments: viewModel.isLoadingComments,
            hasMoreComments: viewModel.hasMoreComments,
            onLoadMoreComments: handleLoadMoreComments,
            loadingRepliesForComment: viewModel.loadingRepliesForComment,
            onLoadMoreReplies: handleLoadMoreReplies
        )
    }

    // MARK: - Navigation & Overlays

    /// 动态颜色的返回按钮
    private var customBackButton: some View {
        Button(action: { dismiss() }) {
            Image(systemName: "chevron.left")
                .font(.system(size: DesignSystemConstants.Navigation.backButtonIconSize,
                             weight: DesignSystemConstants.Navigation.backButtonFontWeight))
                .foregroundColor(isNavigationBarVisible ? .primary : .white)
                .padding(.leading, 8)
                .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }

    @ViewBuilder
    private var fullScreenImageViewer: some View {
        FullScreenImageViewerView(
            imageUrls: viewModel.imageUrls,
            currentPage: $viewModel.currentPage,
            isPresented: $viewModel.showFullScreenViewer,
            onZoomChanged: { viewModel.handleZoomChanged($0) }
        )
    }

    // MARK: - Sheet Views

    private var authorProfileSheet: some View {
        let authorInfo = viewModel.authorInfo
        return UserProfileView(
            username: authorInfo.username,
            avatarUrl: authorInfo.avatarUrl,
            isCurrentUser: false,
            contentTags: viewModel.topic.map { [$0.name] } ?? []
        )
    }

    @ViewBuilder
    private var journeyDetailSheet: some View {
        if let journey = viewModel.journey {
            JourneyDetailView(journey: journey)
        }
    }

    // MARK: - Helper Methods

    private func setupInitialState() {
        Task {
            await viewModel.loadDetailContent()
        }

        let imageCount = viewModel.imageUrls.count
        if viewModel.currentPage >= imageCount {
            viewModel.currentPage = 0
        }
    }

    private var isCommentTextValid: Bool {
        !viewModel.commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    private func handleCommentSubmission() {
        if isCommentTextValid {
            viewModel.handleCommentSubmission()
        }
    }

    /// 处理加载更多评论
    private func handleLoadMoreComments() {
        Task {
            await viewModel.loadComments(refresh: false)
        }
    }

    /// 处理加载更多回复
    private func handleLoadMoreReplies(for commentId: String) {
        Task {
            await viewModel.loadCommentReplies(for: commentId, refresh: false)
        }
    }

}

// MARK: - Preview

#Preview {
    DiscoverDetailContentView(
        navigationParams: DiscoverDetailModels.NavigationParams(
            id: "preview_001",
            previewData: DiscoverDetailModels.PreviewData(
                title: "美丽的自然风光探索之旅",
                imageUrls: ["https://c-ssl.dtstatic.com/uploads/blog/202408/14/9WSP7q6eh8wwMP6.thumb.1000_0.jpg"],
                authorName: "旅行达人",
                authorAvatar: "https://img1.baidu.com/it/u=1747081318,2650263390&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500"
            )
        )
    )
}
