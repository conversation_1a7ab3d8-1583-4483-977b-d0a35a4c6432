import SwiftUI
import Combine

/// 发现详情页视图模型
///
/// 负责管理详情页的业务逻辑，包括点赞、评论、分享、Fork等功能。
/// 遵循MVVM架构，将业务逻辑从View中分离。
/// 使用独立的数据模型和服务，通过导航参数获取详情数据。
@MainActor
class DiscoverDetailViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 详情内容数据
    @Published var detailContent: DiscoverDetailModels.DetailContent?

    /// 评论列表
    @Published var comments: [DiscoverDetailModels.Comment] = []

    /// 当前页码（图片轮播）
    @Published var currentPage: Int = 0

    /// 评论输入文本
    @Published var commentText: String = ""

    /// 是否显示评论输入框
    @Published var showCommentInput: Bool = false

    /// 是否显示作者个人资料
    @Published var isAuthorProfilePresented: Bool = false

    /// 是否自动滚动
    @Published var isAutoScrolling: Bool = true

    /// 是否显示全屏图片查看器
    @Published var showFullScreenViewer: Bool = false

    /// 加载状态
    @Published var isLoading: Bool = false

    /// 评论加载状态
    @Published var isLoadingComments: Bool = false

    /// 错误信息
    @Published var error: String?

    /// 是否显示旅程路线详情
    @Published var showJourneyDetail: Bool = false

    // MARK: - Computed Properties

    /// 预览数据（从导航参数获取）
    var previewData: DiscoverDetailModels.PreviewData? {
        navigationParams.previewData
    }

    // MARK: - Private Properties

    /// 导航参数
    private let navigationParams: DiscoverDetailModels.NavigationParams

    /// 详情页服务
    private let detailService: DiscoverDetailServiceProtocol

    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()

    /// 评论分页信息
    private var currentCommentsPage = 1
    @Published var hasMoreComments = true

    /// 回复相关状态
    @Published var loadingRepliesForComment: Set<String> = []
    private var currentRepliesPages: [String: Int] = [:]
    private var hasMoreRepliesForComment: [String: Bool] = [:]

    var hasMultipleImages: Bool {
        (detailContent?.images.count ?? 0) > 1
    }

    // MARK: - Content Data Properties

    var imageUrls: [String] {
        // 优先使用详情数据中的顶级 imageUrls 字段（与发现列表页保持一致）
        if let content = detailContent {
            let topLevelUrls = content.topLevelImageUrls
            if !topLevelUrls.isEmpty {
                return topLevelUrls
            }
        }

        // 如果详情数据未加载或为空，使用预加载数据中的图片
        if let previewImages = previewData?.imageUrls, !previewImages.isEmpty {
            return previewImages
        }

        return []
    }

    var authorInfo: (avatarUrl: String, username: String) {
        // 优先使用详情数据中的作者信息
        if let author = detailContent?.author {
            return (author.avatarUrl ?? "", author.username)
        }

        // 如果详情数据未加载，使用预加载数据中的作者信息
        if let previewData = previewData {
            return (previewData.authorAvatar ?? "", previewData.authorName)
        }

        return ("", "")
    }

    var contentInfo: (title: String, content: String, location: String?) {
        // 优先使用详情数据中的内容信息
        if let detailContent = detailContent {
            return (detailContent.title, detailContent.content, detailContent.location?.name)
        }

        // 如果详情数据未加载，使用预加载数据中的标题
        if let previewData = previewData {
            return (previewData.title, "", nil) // 预加载数据中没有内容和位置信息
        }

        return ("", "", nil)
    }

    var stats: (likeCount: Int, commentCount: Int, forkCount: Int, shareCount: Int) {
        guard let content = detailContent else {
            return (0, 0, 0, 0)
        }

        // 统一使用 stats.likeCount 字段
        return (content.likeCount, content.stats.commentCount, content.stats.forkCount, content.stats.shareCount)
    }

    var journey: DiscoverDetailModels.JourneyRoute? {
        detailContent?.journey
    }

    var topic: Topic? {
        detailContent?.topic
    }

    var isLiked: Bool {
        get { detailContent?.stats.userInteraction.isLiked ?? false }
        set {
            if var content = detailContent {
                content.stats.userInteraction.isLiked = newValue
                detailContent = content
            }
        }
    }

    var isCollected: Bool {
        get { detailContent?.stats.userInteraction.isCollected ?? false }
        set {
            if var content = detailContent {
                content.stats.userInteraction.isCollected = newValue
                detailContent = content
            }
        }
    }

    var isFollowingAuthor: Bool {
        get { detailContent?.stats.userInteraction.isFollowingAuthor ?? false }
        set {
            if var content = detailContent {
                content.stats.userInteraction.isFollowingAuthor = newValue
                detailContent = content
            }
        }
    }

    var hasJourney: Bool { detailContent?.hasJourney ?? false }

    // MARK: - Initialization

    /// 初始化视图模型
    /// - Parameters:
    ///   - navigationParams: 导航参数
    ///   - detailService: 详情页服务实例
    public init(
        navigationParams: DiscoverDetailModels.NavigationParams,
        detailService: DiscoverDetailServiceProtocol = DiscoverDetailService()
    ) {
        self.navigationParams = navigationParams
        self.detailService = detailService

        setupBindings()

        // 自动加载详情数据
        Task {
            await loadDetailContent()
        }
    }
    
    // MARK: - Public Methods

    /// 加载详情内容
    func loadDetailContent() async {
        guard !isLoading else { return }

        isLoading = true
        error = nil

        do {
            let content = try await detailService.fetchDetailContent(
                id: navigationParams.id
            )

            await MainActor.run {
                self.detailContent = content
                self.isLoading = false

                // 自动加载评论
                Task {
                    await self.loadComments()
                }
            }

        } catch {
            await MainActor.run {
                self.error = error.localizedDescription
                self.isLoading = false
                Log.error("❌ [DiscoverDetailViewModel] 详情内容加载失败: \(error)")
            }
        }
    }

    /// 加载评论列表
    func loadComments(refresh: Bool = false) async {
        guard !isLoadingComments else { return }

        if refresh {
            currentCommentsPage = 1
            hasMoreComments = true
        }

        guard hasMoreComments else { return }

        isLoadingComments = true

        do {
            let response = try await detailService.fetchComments(
                contentId: navigationParams.id,
                page: currentCommentsPage,
                pageSize: 20
            )

            await MainActor.run {
                if refresh {
                    self.comments = response.items
                } else {
                    self.comments.append(contentsOf: response.items)
                }

                self.currentCommentsPage += 1
                self.hasMoreComments = response.hasMore
                self.isLoadingComments = false
            }

        } catch {
            await MainActor.run {
                self.isLoadingComments = false
                Log.error("❌ [DiscoverDetailViewModel] 评论加载失败: \(error)")
            }
        }
    }

    /// 加载评论回复
    func loadCommentReplies(for commentId: String, refresh: Bool = false) async {
        // 防止重复加载
        guard !loadingRepliesForComment.contains(commentId) else { return }

        if refresh {
            currentRepliesPages[commentId] = 1
            hasMoreRepliesForComment[commentId] = true
        }

        // 检查是否还有更多回复
        guard hasMoreRepliesForComment[commentId] != false else { return }

        await MainActor.run {
            loadingRepliesForComment.insert(commentId)
        }

        let currentPage = currentRepliesPages[commentId] ?? 1

        do {
            let response = try await detailService.fetchCommentReplies(
                contentId: navigationParams.id,
                commentId: commentId,
                page: currentPage,
                pageSize: 10
            )

            await MainActor.run {
                // 找到对应的评论并更新回复
                if let commentIndex = self.comments.firstIndex(where: { $0.id == commentId }) {
                    var updatedComment = self.comments[commentIndex]

                    if refresh {
                        // 刷新时替换所有回复
                        updatedComment = DiscoverDetailModels.Comment(
                            id: updatedComment.id,
                            content: updatedComment.content,
                            author: updatedComment.author,
                            createdAt: updatedComment.createdAt,
                            likeCount: updatedComment.likeCount,
                            isLiked: updatedComment.isLiked,
                            replies: response.items,
                            replyCount: updatedComment.replyCount,
                            hasMoreReplies: response.hasMore
                        )
                    } else {
                        // 追加新回复
                        let allReplies = updatedComment.replies + response.items
                        updatedComment = DiscoverDetailModels.Comment(
                            id: updatedComment.id,
                            content: updatedComment.content,
                            author: updatedComment.author,
                            createdAt: updatedComment.createdAt,
                            likeCount: updatedComment.likeCount,
                            isLiked: updatedComment.isLiked,
                            replies: allReplies,
                            replyCount: updatedComment.replyCount,
                            hasMoreReplies: response.hasMore
                        )
                    }

                    self.comments[commentIndex] = updatedComment
                }

                // 更新分页状态
                self.currentRepliesPages[commentId] = currentPage + 1
                self.hasMoreRepliesForComment[commentId] = response.hasMore
                self.loadingRepliesForComment.remove(commentId)
            }

        } catch {
            await MainActor.run {
                self.loadingRepliesForComment.remove(commentId)
                Log.error("❌ [DiscoverDetailViewModel] 回复加载失败: \(error)")
            }
        }
    }

    /// 处理点赞操作
    func handleLike() {
        Task {
            await toggleLike()
        }
    }

    /// 处理收藏操作
    func handleCollect() {
        Task {
            await toggleCollect()
        }
    }

    /// 处理关注作者操作
    func handleFollowAuthor() {
        Task {
            await toggleFollowAuthor()
        }
    }

    /// 处理Fork旅程操作
    func handleForkJourney() {
        guard let journey = detailContent?.journey else { return }

        Task {
            await forkJourney(journeyId: journey.id)
        }
    }

    /// 处理评论提交
    func handleCommentSubmission() {
        guard !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        Task {
            await submitComment()
        }
    }

    /// 处理分享操作
    func handleShare() {
        Task {
            await shareContent()
        }
    }

    /// 显示作者个人资料
    func showAuthorProfile() {
        isAuthorProfilePresented = true
    }

    /// 显示旅程路线详情
    func showJourneyRouteDetail() {
        showJourneyDetail = true
    }

    /// 显示全屏图片查看器
    func showFullScreenImageViewer() {
        showFullScreenViewer = true
    }

    /// 处理缩放状态变化
    func handleZoomChanged(_ isZoomed: Bool) {
        if !showFullScreenViewer {
            isAutoScrolling = !isZoomed
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置数据绑定
    private func setupBindings() {
        // 监听全屏查看器状态变化
        $showFullScreenViewer
            .sink { [weak self] isShowing in
                guard let self = self else { return }
                if isShowing {
                    // 显示全屏查看器时，停止自动滚动
                    self.isAutoScrolling = false
                } else if self.hasMultipleImages {
                    // 关闭全屏查看器时，如果有多张图片则恢复自动滚动
                    self.isAutoScrolling = true
                }
            }
            .store(in: &cancellables)
    }
    
    /// 切换点赞状态
    private func toggleLike() async {
        guard let content = detailContent else { return }

        let currentLikedState = content.stats.userInteraction.isLiked

        do {
            let newLikedState = try await detailService.toggleLike(
                contentId: navigationParams.id,
                isLiked: currentLikedState
            )

            await MainActor.run {
                // 更新本地状态
                self.detailContent?.stats.userInteraction.isLiked = newLikedState
                if newLikedState {
                    self.detailContent?.stats.likeCount += 1
                } else {
                    self.detailContent?.stats.likeCount = max(0, (self.detailContent?.stats.likeCount ?? 0) - 1)
                }

                ToastManager.shared.show(
                    newLikedState ? "已点赞" : "已取消点赞",
                    style: .success
                )
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("操作失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 点赞操作失败: \(error)")
            }
        }
    }
    
    /// 切换收藏状态
    private func toggleCollect() async {
        guard let content = detailContent else { return }

        let currentCollectedState = content.stats.userInteraction.isCollected

        do {
            let newCollectedState = try await detailService.toggleCollect(
                contentId: navigationParams.id,
                isCollected: currentCollectedState
            )

            await MainActor.run {
                // 更新本地状态
                self.detailContent?.stats.userInteraction.isCollected = newCollectedState
                if newCollectedState {
                    self.detailContent?.stats.collectCount += 1
                } else {
                    self.detailContent?.stats.collectCount = max(0, (self.detailContent?.stats.collectCount ?? 0) - 1)
                }

                ToastManager.shared.show(
                    newCollectedState ? "已收藏" : "已取消收藏",
                    style: .success
                )
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("操作失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 收藏操作失败: \(error)")
            }
        }
    }

    /// 切换关注作者状态
    private func toggleFollowAuthor() async {
        guard let content = detailContent else { return }

        let currentFollowingState = content.stats.userInteraction.isFollowingAuthor

        do {
            let newFollowingState = try await detailService.toggleFollowAuthor(
                authorId: content.author.id,
                isFollowing: currentFollowingState
            )

            await MainActor.run {
                // 更新本地状态
                self.detailContent?.stats.userInteraction.isFollowingAuthor = newFollowingState

                ToastManager.shared.show(
                    newFollowingState ? "已关注" : "已取消关注",
                    style: .success
                )
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("操作失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 关注操作失败: \(error)")
            }
        }
    }

    /// Fork旅程路线
    private func forkJourney(journeyId: String) async {
        do {
            _ = try await detailService.forkJourney(journeyId: journeyId)

            await MainActor.run {
                // 更新Fork数量
                self.detailContent?.stats.forkCount += 1

                ToastManager.shared.show("旅程已Fork到您的行程中", style: .success)

                // 可以在这里导航到新的行程页面
                // NavigationManager.shared.navigateToItinerary(id: newJourneyId)
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("Fork失败，请重试", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] Fork操作失败: \(error)")
            }
        }
    }

    /// 提交评论
    private func submitComment() async {
        let content = commentText.trimmingCharacters(in: .whitespacesAndNewlines)

        // 清空输入框
        await MainActor.run {
            self.commentText = ""
            self.showCommentInput = false
        }

        do {
            let newComment = try await detailService.submitComment(
                contentId: navigationParams.id,
                content: content,
                parentCommentId: nil
            )

            await MainActor.run {
                // 将新评论添加到列表顶部
                self.comments.insert(newComment, at: 0)

                // 更新评论数量
                self.detailContent?.stats.commentCount += 1

                ToastManager.shared.show("评论发布成功", style: .success)
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("评论发布失败，请重试", style: .error)
                // 恢复输入内容
                self.commentText = content
                Log.error("❌ [DiscoverDetailViewModel] 评论提交失败: \(error)")
            }
        }
    }

    /// 分享内容
    private func shareContent() async {
        do {
            let shareUrl = try await detailService.shareContent(contentId: navigationParams.id, platform: nil)

            await MainActor.run {
                // 更新分享数量
                self.detailContent?.stats.shareCount += 1

                // 调用系统分享
                self.presentSystemShare(url: shareUrl)
            }
        } catch {
            await MainActor.run {
                ToastManager.shared.show("分享链接生成失败", style: .error)
                Log.error("❌ [DiscoverDetailViewModel] 分享操作失败: \(error)")
            }
        }
    }

    /// 调用系统分享
    private func presentSystemShare(url: String) {
        // 这里可以调用系统分享功能
        // 或者复制到剪贴板
        UIPasteboard.general.string = url
        Task { @MainActor in
            ToastManager.shared.show("分享链接已复制到剪贴板", style: .success)
        }
    }

    // MARK: - Preview Support

    /// 创建预览用的视图模型
    @MainActor
    static func preview() -> DiscoverDetailViewModel {
        let mockParams = DiscoverDetailModels.NavigationParams(
            id: "preview_001",
            previewData: DiscoverDetailModels.PreviewData(
                title: "预览标题",
                imageUrls: ["https://images.unsplash.com/photo-1506905925346-21bda4d32df4"],
                authorName: "预览用户",
                authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e"
            )
        )
        return DiscoverDetailViewModel(navigationParams: mockParams)
    }
}
