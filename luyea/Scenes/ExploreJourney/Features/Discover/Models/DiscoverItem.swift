import Foundation

// MARK: - 瀑布流轻量级模型
/// 瀑布流列表项模型 - 只包含列表展示必需的数据
struct DiscoverListItem: Identifiable, Equatable, Codable, Hashable {
    let id: String
    let imageUrls: [String]    // 图片展示
    let title: String          // 标题显示
    let content: String        // 摘要显示（服务端处理长度）
    let likeCount: Int        // 点赞数显示（统一使用likeCount）
    let location: String?     // 位置标签
    let topic: Topic?         // 话题标签
    let allowFork: Bool       // "包含行程"筛选

    init(
        id: String,
        imageUrls: [String],
        title: String,
        content: String,
        likeCount: Int,
        location: String? = nil,
        topic: Topic? = nil,
        allowFork: Bool = false
    ) {
        self.id = id
        self.imageUrls = imageUrls
        self.title = title
        self.content = content
        self.likeCount = likeCount
        self.location = location
        self.topic = topic
        self.allowFork = allowFork
    }

    static func == (lhs: DiscoverListItem, rhs: DiscoverListItem) -> Bool {
        lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

// MARK: - 数据转换扩展
extension DiscoverItem {
    /// 转换为轻量级列表项
    func toListItem() -> DiscoverListItem {
        return DiscoverListItem(
            id: id,
            imageUrls: imageUrls,
            title: title,
            content: content,
            likeCount: likeCount,
            location: location,
            topic: topic,
            allowFork: allowFork
        )
    }
}

// MARK: - 详情页完整模型
/// 发现详情页模型 - 包含完整数据
struct DiscoverItem: Identifiable, Equatable, Codable, Hashable {
    let id: String
    let imageUrls: [String]
    let title: String
    let username: String
    let userAvatarUrl: String
    let likeCount: Int        // 统一使用likeCount
    let content: String
    let location: String?
    let comments: [Comment]
    let topic: Topic?
    let journey: JourneyRoute?
    let forkCount: Int
    let allowFork: Bool

    init(
        id: String,
        imageUrls: [String],
        title: String,
        username: String,
        userAvatarUrl: String,
        likeCount: Int,
        content: String,
        location: String? = nil,
        comments: [Comment] = [],
        topic: Topic? = nil,
        journey: JourneyRoute? = nil,
        forkCount: Int = 0,
        allowFork: Bool = false
    ) {
        self.id = id
        self.imageUrls = imageUrls
        self.title = title
        self.username = username
        self.userAvatarUrl = userAvatarUrl
        self.likeCount = likeCount
        self.content = content
        self.location = location
        self.comments = comments
        self.topic = topic
        self.journey = journey
        self.forkCount = forkCount
        self.allowFork = allowFork
    }
    
    static func == (lhs: DiscoverItem, rhs: DiscoverItem) -> Bool {
        lhs.id == rhs.id &&
        lhs.imageUrls == rhs.imageUrls &&
        lhs.title == rhs.title &&
        lhs.username == rhs.username &&
        lhs.userAvatarUrl == rhs.userAvatarUrl &&
        lhs.likeCount == rhs.likeCount &&
        lhs.content == rhs.content &&
        lhs.location == rhs.location &&
        lhs.comments == rhs.comments &&
        lhs.topic == rhs.topic &&
        lhs.journey == rhs.journey &&
        lhs.forkCount == rhs.forkCount &&
        lhs.allowFork == rhs.allowFork
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    enum CodingKeys: String, CodingKey {
        case id, imageUrls, title, username, userAvatarUrl, likeCount, content, location, comments, topic, journey, forkCount, allowFork
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        // 支持从字符串或数值解析ID
        if let idString = try? container.decode(String.self, forKey: .id) {
            self.id = idString
        } else if let idInt = try? container.decode(Int.self, forKey: .id) {
            self.id = "\(idInt)"
        } else {
            self.id = "0" // 默认ID
        }
        self.imageUrls = try container.decode([String].self, forKey: .imageUrls)
        self.title = try container.decode(String.self, forKey: .title)
        self.username = try container.decode(String.self, forKey: .username)
        self.userAvatarUrl = try container.decode(String.self, forKey: .userAvatarUrl)
        self.likeCount = try container.decode(Int.self, forKey: .likeCount)
        self.content = try container.decode(String.self, forKey: .content)
        self.location = try container.decodeIfPresent(String.self, forKey: .location)
        self.comments = try container.decode([Comment].self, forKey: .comments)
        self.topic = try container.decodeIfPresent(Topic.self, forKey: .topic)
        self.journey = try container.decodeIfPresent(JourneyRoute.self, forKey: .journey)
        self.forkCount = try container.decodeIfPresent(Int.self, forKey: .forkCount) ?? 0
        self.allowFork = try container.decodeIfPresent(Bool.self, forKey: .allowFork) ?? false
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(imageUrls, forKey: .imageUrls)
        try container.encode(title, forKey: .title)
        try container.encode(username, forKey: .username)
        try container.encode(userAvatarUrl, forKey: .userAvatarUrl)
        try container.encode(likeCount, forKey: .likeCount)
        try container.encode(content, forKey: .content)
        try container.encode(location, forKey: .location)
        try container.encode(comments, forKey: .comments)
        try container.encode(topic, forKey: .topic)
        try container.encode(journey, forKey: .journey)
        try container.encode(forkCount, forKey: .forkCount)
        try container.encode(allowFork, forKey: .allowFork)
    }
}

struct Comment: Identifiable, Equatable, Codable, Hashable {
    let id: UUID
    let username: String
    let content: String
    let timestamp: Date
    
    init(id: UUID = UUID(), username: String, content: String, timestamp: Date = Date()) {
        self.id = id
        self.username = username
        self.content = content
        self.timestamp = timestamp
    }
    
    static func == (lhs: Comment, rhs: Comment) -> Bool {
        lhs.id == rhs.id &&
        lhs.username == rhs.username &&
        lhs.content == rhs.content &&
        lhs.timestamp == rhs.timestamp
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    enum CodingKeys: String, CodingKey {
        case id, username, content, timestamp
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        if let id = try? container.decode(UUID.self, forKey: .id) {
            self.id = id
        } else {
            self.id = UUID()
        }
        self.username = try container.decode(String.self, forKey: .username)
        self.content = try container.decode(String.self, forKey: .content)

        // 处理 timestamp 的多种格式
        if let timestampValue = try? container.decodeIfPresent(Double.self, forKey: .timestamp) {
            // 时间戳格式（秒）
            self.timestamp = Date(timeIntervalSince1970: timestampValue)
        } else if let timestampString = try? container.decodeIfPresent(String.self, forKey: .timestamp) {
            // ISO8601 字符串格式
            let formatter = ISO8601DateFormatter()
            self.timestamp = formatter.date(from: timestampString) ?? Date()
        } else {
            // 默认为当前时间
            self.timestamp = Date()
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(username, forKey: .username)
        try container.encode(content, forKey: .content)
        try container.encode(timestamp, forKey: .timestamp)
    }
}

// MARK: - Journey Route Models

/// 旅程路线数据模型
struct JourneyRoute: Identifiable, Equatable, Codable, Hashable {
    let id: UUID
    let title: String
    let content: String?
    let duration: String // 如："3天2夜"
    let destinations: [RouteDestination]
    let totalDistance: Double? // 总距离（公里）
    let estimatedCost: Double? // 预估费用
    let difficulty: RouteDifficulty
    let tags: [String]
    let createdAt: Date
    let updatedAt: Date?

    init(
        id: UUID = UUID(),
        title: String,
        content: String? = nil,
        duration: String,
        destinations: [RouteDestination],
        totalDistance: Double? = nil,
        estimatedCost: Double? = nil,
        difficulty: RouteDifficulty = .easy,
        tags: [String] = [],
        createdAt: Date = Date(),
        updatedAt: Date? = nil
    ) {
        self.id = id
        self.title = title
        self.content = content
        self.duration = duration
        self.destinations = destinations
        self.totalDistance = totalDistance
        self.estimatedCost = estimatedCost
        self.difficulty = difficulty
        self.tags = tags
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    static func == (lhs: JourneyRoute, rhs: JourneyRoute) -> Bool {
        lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

/// 路线目的地
struct RouteDestination: Identifiable, Codable, Equatable, Hashable {
    let id: UUID
    let name: String
    let location: String
    let coordinate: Coordinate?
    let order: Int // 在路线中的顺序
    let stayDuration: String? // 停留时间，如："2小时"
    let content: String?
    let imageUrl: String?
    let attractions: [String] // 主要景点

    init(
        id: UUID = UUID(),
        name: String,
        location: String,
        coordinate: Coordinate? = nil,
        order: Int,
        stayDuration: String? = nil,
        content: String? = nil,
        imageUrl: String? = nil,
        attractions: [String] = []
    ) {
        self.id = id
        self.name = name
        self.location = location
        self.coordinate = coordinate
        self.order = order
        self.stayDuration = stayDuration
        self.content = content
        self.imageUrl = imageUrl
        self.attractions = attractions
    }

    static func == (lhs: RouteDestination, rhs: RouteDestination) -> Bool {
        lhs.id == rhs.id
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

/// 路线难度等级
enum RouteDifficulty: String, Codable, CaseIterable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"

    var displayName: String {
        switch self {
        case .easy: return "轻松"
        case .medium: return "中等"
        case .hard: return "困难"
        }
    }

    var color: String {
        switch self {
        case .easy: return "green"
        case .medium: return "orange"
        case .hard: return "red"
        }
    }

    var icon: String {
        switch self {
        case .easy: return "leaf"
        case .medium: return "figure.walk"
        case .hard: return "mountain.2"
        }
    }
}

// MARK: - Note: Coordinate Model
// 使用Core层统一的Coordinate模型，无需重复定义