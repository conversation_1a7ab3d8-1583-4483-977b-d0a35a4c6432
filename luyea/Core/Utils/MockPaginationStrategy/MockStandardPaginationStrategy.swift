import Foundation

// MARK: - Mock标准分页策略

/// Mock标准分页策略
///
/// 使用统一的MockQueryParam配置，将筛选和排序操作整合在一起管理
/// 这种设计更加清晰，避免了概念混淆和代码重复
///
/// ## 设计优势
/// - 统一参数管理：筛选和排序使用相同的配置结构
/// - 类型安全：明确区分筛选和排序操作
/// - 易于扩展：新增操作类型只需扩展枚举
/// - 配置简洁：一个数组管理所有查询参数
///
/// ## 使用示例
/// ```swift
/// let config = MockPaginationConfig(
///     name: "我的作品列表",
///     apiPath: "/api/my-works",
///     modelType: WorkItem.self,
///     queryParams: [
///         .filter(key: "status", handler: .status("status")),
///         .filter(key: "keyword", handler: .keyword(["title", "description"])),
///         .sort(key: "create_time", handler: .byDate("createdAt")),
///         .sort(key: "fork_count", handler: .custom({ item1, item2, ascending in
///             // 自定义排序逻辑
///         }))
///     ]
/// )
/// ```
struct MockStandardPaginationStrategy<T: Codable>: MockPaginationStrategy {
    typealias ItemType = T

    let config: MockPaginationConfig<T>

    init(config: MockPaginationConfig<T>) {
        self.config = config
    }

    var name: String { config.name }
    var itemType: T.Type { config.modelType }

    func canHandle(apiPath: String) -> Bool {
        return MockURIPatternMatcher.shared.matchPattern(uri: apiPath, pattern: config.apiPath) != nil
    }

    func applyFilters(to items: [T], with queryParams: [String: String]) -> [T] {
        var processedItems = items

        // 第一阶段：应用所有筛选操作
        let filterParams = config.queryParams.filter { $0.operation == .filter }
        for param in filterParams {
            guard let paramValue = queryParams[param.key],
                  case .filter(let filterHandler) = param.handler else { continue }

            processedItems = applyFilter(items: processedItems, handler: filterHandler, value: paramValue)
        }

        // 第二阶段：应用排序操作（如果有sortBy参数）
        if let sortBy = queryParams["sortBy"] {
            let order = queryParams["order"] ?? MockPaginationStrategyConstants.defaultSortOrder
            
            // 查找对应的排序参数
            if let sortParam = config.queryParams.first(where: { 
                $0.key == sortBy && $0.operation == .sort 
            }),
               case .sort(let sortHandler) = sortParam.handler {
                
                Log.debug("📊 [MockStandardStrategy] 开始应用排序: sortBy=\(sortBy), order=\(order), 数据量=\(processedItems.count)")

                processedItems = applySorting(items: processedItems, handler: sortHandler, ascending: order.lowercased() == "asc")

                Log.debug("📊 [MockStandardStrategy] 排序完成: 数据量=\(processedItems.count)")
            } else {
                Log.warning("⚠️ [MockStandardStrategy] 未找到排序参数: \(sortBy)")
            }
        } else {
            Log.debug("📊 [MockStandardStrategy] 没有sortBy参数，跳过排序")
        }

        return processedItems
    }

    // MARK: - 筛选处理

    /// 应用筛选操作
    private func applyFilter(items: [T], handler: MockFilterHandler, value: String) -> [T] {
        switch handler {
        case .exact(let fieldPath):
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) else { return false }
                return "\(fieldValue)" == value
            }

        case .contains(let fieldPath):
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) as? String else { return false }
                return fieldValue.localizedCaseInsensitiveContains(value)
            }

        case .keyword(let fieldPaths):
            let lowercaseKeyword = value.lowercased()
            return items.filter { item in
                return fieldPaths.contains { fieldPath in
                    guard let fieldValue = getFieldValue(from: item, path: fieldPath) as? String else { return false }
                    return fieldValue.lowercased().contains(lowercaseKeyword)
                }
            }

        case .status(let fieldPath):
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) else { return false }
                return "\(fieldValue)" == value
            }

        case .range(let fieldPath, let range):
            guard let _ = Double(value) else { return items }
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath),
                      let fieldNumeric = convertToDouble(fieldValue) else { return false }
                return range.contains(fieldNumeric)
            }

        case .dateRange(let fieldPath, let range):
            let formatter = ISO8601DateFormatter()
            guard formatter.date(from: value) != nil else { return items }
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) as? Date else { return false }
                return range.contains(fieldValue)
            }

        case .arrayContains(let fieldPath):
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) else { return false }

                if let stringArray = fieldValue as? [String] {
                    return stringArray.contains(value)
                } else if let anyArray = fieldValue as? [Any] {
                    return anyArray.contains { "\($0)" == value }
                }
                return false
            }

        case .boolean(let fieldPath):
            let boolValue = value.lowercased() == "true" || value == "1"
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) as? Bool else { return false }
                return fieldValue == boolValue
            }

        case .enumMatch(let fieldPath):
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) else { return false }
                return "\(fieldValue)" == value
            }

        case .multiSelect(let fieldPath, let options):
            let selectedValues = value.split(separator: ",").map { String($0).trimmingCharacters(in: .whitespaces) }
            return items.filter { item in
                guard let fieldValue = getFieldValue(from: item, path: fieldPath) else { return false }
                let fieldString = "\(fieldValue)"
                return selectedValues.contains(fieldString) && options.contains(fieldString)
            }

        case .custom(let filterFunc):
            return items.filter { filterFunc($0, value) }
        }
    }

    // MARK: - 排序处理

    /// 应用排序操作
    private func applySorting(items: [T], handler: MockSortHandler, ascending: Bool) -> [T] {
        switch handler {
        case .byNumber(let fieldPath):
            return sortByNumber(items: items, fieldPath: fieldPath, ascending: ascending)

        case .byDate(let fieldPath):
            return sortByDate(items: items, fieldPath: fieldPath, ascending: ascending)

        case .byString(let fieldPath):
            return sortByString(items: items, fieldPath: fieldPath, ascending: ascending)

        case .byCount(let fieldPath):
            return sortByCount(items: items, fieldPath: fieldPath, ascending: ascending)

        case .byOptional(let fieldPath, let defaultValue):
            return sortByOptional(items: items, fieldPath: fieldPath, defaultValue: defaultValue, ascending: ascending)

        case .custom(let sortFunction):
            return items.sorted { item1, item2 in
                return sortFunction(item1, item2, ascending)
            }
        }
    }
}

// MARK: - 工具方法扩展

extension MockStandardPaginationStrategy {

    /// 从对象中获取指定路径的字段值
    private func getFieldValue(from object: Any, path: String) -> Any? {
        let pathComponents = path.split(separator: MockPaginationStrategyConstants.pathSeparator).map(String.init)
        var currentValue: Any? = object

        for component in pathComponents {
            guard let current = currentValue else { return nil }
            currentValue = extractFieldValue(from: current, fieldName: component)
            if currentValue == nil { return nil }
        }

        return currentValue
    }

    /// 从单个对象中提取指定字段值
    private func extractFieldValue(from object: Any, fieldName: String) -> Any? {
        let mirror = Mirror(reflecting: object)
        return mirror.children.first { $0.label == fieldName }?.value
    }

    /// 将值转换为Double
    private func convertToDouble(_ value: Any?) -> Double? {
        guard let value = value else { return nil }

        switch value {
        case let doubleValue as Double:
            return doubleValue
        case let intValue as Int:
            return Double(intValue)
        case let int8Value as Int8:
            return Double(int8Value)
        case let int16Value as Int16:
            return Double(int16Value)
        case let int32Value as Int32:
            return Double(int32Value)
        case let int64Value as Int64:
            return Double(int64Value)
        case let uintValue as UInt:
            return Double(uintValue)
        case let floatValue as Float:
            return Double(floatValue)
        case let cgFloatValue as CGFloat:
            return Double(cgFloatValue)
        case let stringValue as String:
            return Double(stringValue.trimmingCharacters(in: .whitespacesAndNewlines))
        case let nsNumberValue as NSNumber:
            return nsNumberValue.doubleValue
        default:
            return nil
        }
    }

    /// 按数值排序
    private func sortByNumber(items: [T], fieldPath: String, ascending: Bool) -> [T] {
        return items.sorted { item1, item2 in
            let value1 = getFieldValue(from: item1, path: fieldPath)
            let value2 = getFieldValue(from: item2, path: fieldPath)

            // 检查是否为空值
            let isEmpty1 = (value1 == nil)
            let isEmpty2 = (value2 == nil)

            // 空值排序逻辑
            if isEmpty1 && isEmpty2 {
                return false
            } else if isEmpty1 {
                return false
            } else if isEmpty2 {
                return true
            }

            // 都不是空值，进行数值比较
            let num1 = convertToDouble(value1) ?? 0
            let num2 = convertToDouble(value2) ?? 0

            return ascending ? num1 < num2 : num1 > num2
        }
    }

    /// 按日期排序
    private func sortByDate(items: [T], fieldPath: String, ascending: Bool) -> [T] {
        return items.sorted { item1, item2 in
            let date1 = getFieldValue(from: item1, path: fieldPath) as? Date
            let date2 = getFieldValue(from: item2, path: fieldPath) as? Date

            switch (date1, date2) {
            case (nil, nil): return false
            case (nil, _): return false
            case (_, nil): return true
            case let (d1?, d2?):
                return ascending ? d1 < d2 : d1 > d2
            }
        }
    }

    /// 按字符串排序
    private func sortByString(items: [T], fieldPath: String, ascending: Bool) -> [T] {
        return items.sorted { item1, item2 in
            let string1 = getFieldValue(from: item1, path: fieldPath) as? String
            let string2 = getFieldValue(from: item2, path: fieldPath) as? String

            let isEmpty1 = string1?.isEmpty ?? true
            let isEmpty2 = string2?.isEmpty ?? true

            if isEmpty1 && isEmpty2 {
                return false
            } else if isEmpty1 {
                return false
            } else if isEmpty2 {
                return true
            }

            let str1 = string1 ?? ""
            let str2 = string2 ?? ""
            return ascending ? str1 < str2 : str1 > str2
        }
    }

    /// 按数组长度排序
    private func sortByCount(items: [T], fieldPath: String, ascending: Bool) -> [T] {
        return items.sorted { item1, item2 in
            let value1 = getFieldValue(from: item1, path: fieldPath)
            let value2 = getFieldValue(from: item2, path: fieldPath)

            let count1 = (value1 as? [Any])?.count ?? 0
            let count2 = (value2 as? [Any])?.count ?? 0

            return ascending ? count1 < count2 : count1 > count2
        }
    }

    /// 按可选字段排序
    private func sortByOptional(items: [T], fieldPath: String, defaultValue: Any, ascending: Bool) -> [T] {
        return items.sorted { item1, item2 in
            let value1 = getFieldValue(from: item1, path: fieldPath) ?? defaultValue
            let value2 = getFieldValue(from: item2, path: fieldPath) ?? defaultValue

            if let date1 = value1 as? Date, let date2 = value2 as? Date {
                return ascending ? date1 < date2 : date1 > date2
            } else if let num1 = value1 as? Double, let num2 = value2 as? Double {
                return ascending ? num1 < num2 : num1 > num2
            } else if let int1 = value1 as? Int, let int2 = value2 as? Int {
                return ascending ? int1 < int2 : int1 > int2
            } else if let str1 = value1 as? String, let str2 = value2 as? String {
                return ascending ? str1 < str2 : str1 > str2
            }

            return false
        }
    }
}
