import Foundation

/// Mock URI模式匹配器（单例模式）
///
/// 支持通配符路径匹配，提供更精确和灵活的API路径匹配能力
///
/// ## 支持的模式
/// - 精确匹配: `/api/v1/discover/1/comments`
/// - 通配符匹配: `/api/v1/discover/{id}/comments`
/// - 多参数匹配: `/api/v1/discover/{contentId}/comments/{commentId}/replies`
/// - 通配符结尾: `/api/v1/discover/{id}/*`
///
/// ## 匹配优先级
/// 1. 精确匹配 (权重: 100)
/// 2. 通配符匹配 (权重: 50 + 匹配段数)
/// 3. 包含匹配 (权重: 10)
/// 4. 无匹配 (权重: 0)
///
/// ## 使用示例
/// ```swift
/// // 配置模式
/// let patterns = [
///     "/api/v1/discover/{id}/comments",
///     "/api/v1/discover/{contentId}/comments/{commentId}/replies"
/// ]
///
/// // 匹配测试
/// let result = MockURIPatternMatcher.shared.findBestMatch(
///     for: "/api/v1/discover/1/comments/1/replies",
///     in: patterns
/// )
/// ```
public final class MockURIPatternMatcher {

    // MARK: - 单例

    /// 共享实例
    public static let shared = MockURIPatternMatcher()

    /// 私有初始化器，确保单例模式
    private init() {}
    
    // MARK: - 匹配结果
    
    /// URI匹配结果
    public struct MatchResult {
        /// 匹配的模式
        public let pattern: String
        /// 匹配权重（越高越精确）
        public let weight: Int
        /// 提取的参数
        public let parameters: [String: String]
        /// 匹配类型
        public let matchType: MatchType
        
        public enum MatchType {
            case exact      // 精确匹配
            case wildcard   // 通配符匹配
            case contains   // 包含匹配
        }
    }
    
    // MARK: - 公共接口
    
    /// 在给定模式列表中查找最佳匹配
    /// - Parameters:
    ///   - uri: 要匹配的URI
    ///   - patterns: 模式列表
    /// - Returns: 最佳匹配结果，如果没有匹配则返回nil
    public func findBestMatch(for uri: String, in patterns: [String]) -> MatchResult? {
        let results = patterns.compactMap { pattern in
            matchPattern(uri: uri, pattern: pattern)
        }
        
        // 按权重排序，返回最高权重的匹配
        return results.max { $0.weight < $1.weight }
    }
    
    /// 检查URI是否匹配指定模式
    /// - Parameters:
    ///   - uri: 要检查的URI
    ///   - pattern: 模式字符串
    /// - Returns: 匹配结果，如果不匹配则返回nil
    public func matchPattern(uri: String, pattern: String) -> MatchResult? {
        // 1. 尝试精确匹配
        if uri == pattern {
            return MatchResult(
                pattern: pattern,
                weight: 100,
                parameters: [:],
                matchType: .exact
            )
        }
        
        // 2. 尝试通配符匹配
        if let wildcardResult = matchWildcardPattern(uri: uri, pattern: pattern) {
            return wildcardResult
        }
        
        // 3. 尝试包含匹配（向后兼容）
        if uri.contains(pattern) {
            return MatchResult(
                pattern: pattern,
                weight: 10,
                parameters: [:],
                matchType: .contains
            )
        }
        
        return nil
    }
    
    // MARK: - 私有方法
    
    /// 通配符模式匹配
    private func matchWildcardPattern(uri: String, pattern: String) -> MatchResult? {
        let uriSegments = uri.split(separator: "/").map(String.init)
        let patternSegments = pattern.split(separator: "/").map(String.init)
        
        // 处理通配符结尾的情况
        if patternSegments.last == "*" {
            return matchWildcardSuffix(uriSegments: uriSegments, patternSegments: patternSegments, originalPattern: pattern)
        }
        
        // 段数必须相等（除非有通配符结尾）
        guard uriSegments.count == patternSegments.count else {
            return nil
        }
        
        var parameters: [String: String] = [:]
        var matchedSegments = 0
        
        for (uriSegment, patternSegment) in zip(uriSegments, patternSegments) {
            if patternSegment.hasPrefix("{") && patternSegment.hasSuffix("}") {
                // 通配符参数
                let paramName = String(patternSegment.dropFirst().dropLast())
                parameters[paramName] = uriSegment
                matchedSegments += 1
            } else if uriSegment == patternSegment {
                // 精确匹配的段
                matchedSegments += 1
            } else {
                // 不匹配
                return nil
            }
        }
        
        // 计算权重：基础权重50 + 匹配段数
        let weight = 50 + matchedSegments
        
        return MatchResult(
            pattern: pattern,
            weight: weight,
            parameters: parameters,
            matchType: .wildcard
        )
    }
    
    /// 处理通配符结尾的模式匹配
    private func matchWildcardSuffix(uriSegments: [String], patternSegments: [String], originalPattern: String) -> MatchResult? {
        let prefixSegments = Array(patternSegments.dropLast()) // 移除最后的 "*"
        
        // URI段数必须大于等于前缀段数
        guard uriSegments.count >= prefixSegments.count else {
            return nil
        }
        
        var parameters: [String: String] = [:]
        var matchedSegments = 0
        
        // 匹配前缀部分
        for (index, patternSegment) in prefixSegments.enumerated() {
            let uriSegment = uriSegments[index]
            
            if patternSegment.hasPrefix("{") && patternSegment.hasSuffix("}") {
                // 通配符参数
                let paramName = String(patternSegment.dropFirst().dropLast())
                parameters[paramName] = uriSegment
                matchedSegments += 1
            } else if uriSegment == patternSegment {
                // 精确匹配的段
                matchedSegments += 1
            } else {
                // 不匹配
                return nil
            }
        }
        
        // 计算权重：基础权重40 + 匹配段数（比完全匹配权重稍低）
        let weight = 40 + matchedSegments
        
        return MatchResult(
            pattern: originalPattern,
            weight: weight,
            parameters: parameters,
            matchType: .wildcard
        )
    }
}

// MARK: - 便利扩展

extension MockURIPatternMatcher {

    /// 快速检查URI是否匹配模式
    /// - Parameters:
    ///   - uri: URI字符串
    ///   - pattern: 模式字符串
    /// - Returns: 是否匹配
    public static func matches(uri: String, pattern: String) -> Bool {
        return shared.matchPattern(uri: uri, pattern: pattern) != nil
    }

    /// 从URI中提取参数
    /// - Parameters:
    ///   - uri: URI字符串
    ///   - pattern: 模式字符串
    /// - Returns: 提取的参数字典
    public static func extractParameters(from uri: String, using pattern: String) -> [String: String] {
        return shared.matchPattern(uri: uri, pattern: pattern)?.parameters ?? [:]
    }
}
