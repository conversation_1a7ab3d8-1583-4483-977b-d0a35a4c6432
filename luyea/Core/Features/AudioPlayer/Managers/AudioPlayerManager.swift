import SwiftUI
import UIKit
import Combine

@MainActor
final class AudioPlayerManager: ObservableObject {
    static let shared = AudioPlayerManager()

    @Published private(set) var isVisible = false
    @Published private(set) var playerState: PlayerState = .collapsed
    @Published private(set) var attachmentSide: AttachmentSide = .left
    @Published private(set) var isDragging = false
    @Published private(set) var displayPosition: CGPoint = .zero
    @Published private(set) var audioInfo: AudioInfo?
    @Published private(set) var isPlaying = false
    @Published private(set) var playbackProgress: Double = 0.0

    private var dragStartPosition: CGPoint = .zero
    private var cancellables = Set<AnyCancellable>()

    private init() {
        setupInitialPosition()
        setupServiceStateObservation()
    }

    func showPlayer(with audioInfo: AudioInfo) {
        self.audioInfo = audioInfo

        if !isVisible {
            playerState = .collapsed
            setupInitialPosition()
            withAnimation(.easeInOut(duration: 0.3)) {
                isVisible = true
            }
        }

        Task {
            do {
                try await AudioPlayerService.shared.play(audioInfo: audioInfo)
            } catch {
                Log.error("自动播放失败: \(error)")
            }
        }
    }

    func hidePlayer() {
        AudioPlayerService.shared.stop()
        isPlaying = false

        let edgePosition = calculateHidePosition()

        withAnimation(.spring(response: 0.35, dampingFraction: 0.9)) {
            playerState = .collapsed
            displayPosition = edgePosition
        }

        withAnimation(.easeIn(duration: 0.25).delay(0.2)) {
            isVisible = false
        }
    }

    func togglePlayPause() {
        guard audioInfo != nil else { return }

        do {
            try AudioPlayerService.shared.togglePlayPause()
            isPlaying = AudioPlayerService.shared.isPlaying
        } catch {
            Log.error("播放控制失败: \(error)")
        }
    }

    func expandPlayer() {
        guard playerState == .collapsed else { return }

        let expandedPosition = calculateExpandedPosition()

        withAnimation(.spring(response: 0.45, dampingFraction: 0.8)) {
            playerState = .expanded
            displayPosition = expandedPosition
        }

        updateAttachmentSide(for: expandedPosition)
    }

    func collapsePlayer() {
        guard playerState == .expanded else { return }

        let collapsedPosition = calculateSnapPosition(from: displayPosition, forState: .collapsed)

        withAnimation(.spring(response: 0.4, dampingFraction: 0.85)) {
            playerState = .collapsed
            displayPosition = collapsedPosition
        }

        updateAttachmentSide(for: collapsedPosition)
    }

    func startDragging() {
        guard !isDragging else { return }
        isDragging = true
        dragStartPosition = displayPosition
    }

    func updateDragPosition(_ translation: CGSize) {
        guard isDragging else { return }

        let newPosition = CGPoint(
            x: dragStartPosition.x + translation.width,
            y: dragStartPosition.y + translation.height
        )

        displayPosition = constrainToScreenBounds(newPosition)
    }

    func endDragging() {
        guard isDragging else { return }

        let finalPosition = calculateSnapPosition(from: displayPosition)

        isDragging = false
        displayPosition = finalPosition
        updateAttachmentSide(for: finalPosition)
    }

    private func constrainToScreenBounds(_ position: CGPoint) -> CGPoint {
        let screenBounds = UIScreen.main.bounds
        let margin = AudioPlayerDimensions.safetyMargin
        let radius = AudioPlayerDimensions.collapsedRadius

        let x = max(radius + margin, min(position.x, screenBounds.width - radius - margin))
        let y = max(radius + margin, min(position.y, screenBounds.height - radius - margin))

        return CGPoint(x: x, y: y)
    }

    private func calculateSnapPosition(from position: CGPoint, forState state: PlayerState? = nil) -> CGPoint {
        let screenBounds = UIScreen.main.bounds
        let screenCenter = screenBounds.width / 2
        let targetState = state ?? playerState

        let targetX: CGFloat
        if position.x < screenCenter {
            if targetState == .expanded {
                targetX = AudioPlayerDimensions.expandedMaxWidth / 2
            } else {
                targetX = AudioPlayerDimensions.collapsedRadius
            }
        } else {
            if targetState == .expanded {
                targetX = screenBounds.width - AudioPlayerDimensions.expandedMaxWidth / 2
            } else {
                targetX = screenBounds.width - AudioPlayerDimensions.collapsedRadius
            }
        }

        return CGPoint(x: targetX, y: position.y)
    }

    private func updateAttachmentSide(for position: CGPoint) {
        let screenCenter = UIScreen.main.bounds.width / 2
        attachmentSide = position.x < screenCenter ? .left : .right
    }

    private func calculateHidePosition() -> CGPoint {
        let screenBounds = UIScreen.main.bounds
        let radius = AudioPlayerDimensions.collapsedRadius

        let targetX: CGFloat
        switch attachmentSide {
        case .left:
            targetX = -radius
        case .right:
            targetX = screenBounds.width + radius
        }

        return CGPoint(x: targetX, y: displayPosition.y)
    }

    private func calculateExpandedPosition() -> CGPoint {
        let screenBounds = UIScreen.main.bounds
        let halfWidth = AudioPlayerDimensions.expandedMaxWidth / 2

        let adjustedX: CGFloat
        switch attachmentSide {
        case .left:
            adjustedX = halfWidth
        case .right:
            adjustedX = screenBounds.width - halfWidth
        }

        return CGPoint(x: adjustedX, y: displayPosition.y)
    }

    private func setupInitialPosition() {
        let screenBounds = UIScreen.main.bounds
        let radius = AudioPlayerDimensions.collapsedRadius

        let initialPosition = CGPoint(
            x: radius,
            y: screenBounds.height * 0.2
        )

        displayPosition = initialPosition
        updateAttachmentSide(for: initialPosition)
    }

    private func setupServiceStateObservation() {
        AudioPlayerService.shared.$isPlaying
            .receive(on: DispatchQueue.main)
            .sink { [weak self] serviceIsPlaying in
                self?.isPlaying = serviceIsPlaying
            }
            .store(in: &cancellables)

        AudioPlayerService.shared.$currentAudioInfo
            .receive(on: DispatchQueue.main)
            .sink { [weak self] serviceAudioInfo in
                if let serviceAudioInfo = serviceAudioInfo {
                    self?.audioInfo = serviceAudioInfo
                }
            }
            .store(in: &cancellables)

        AudioPlayerService.shared.$playbackProgress
            .receive(on: DispatchQueue.main)
            .sink { [weak self] progress in
                self?.playbackProgress = progress
            }
            .store(in: &cancellables)
    }
}
