import SwiftUI

/// 右滑返回手势修饰器
///
/// 解决使用 `navigationBarBackButtonHidden(true)` 后系统右滑返回手势被禁用的问题。
/// 专门针对 NavigationStack 设计的 SwiftUI 原生手势实现。
///
/// 核心特性：
/// - 恢复被禁用的右滑返回手势
/// - 与自定义返回按钮行为保持一致
/// - 专用的左边缘手势区域，避免与其他UI冲突
/// - 可配置的启用/禁用选项
///
/// 使用示例：
/// ```swift
/// SomeView()
///     .navigationBarBackButtonHidden(true)
///     .toolbar {
///         ToolbarItem(placement: .navigationBarLeading) {
///             customBackButton
///         }
///     }
///     .enableSwipeBackGesture()
/// ```
struct SwipeBackGestureModifier: ViewModifier {

    // MARK: - Properties

    /// 是否启用右滑返回手势
    private let isEnabled: Bool

    // MARK: - Initialization

    /// 初始化右滑返回手势修饰器
    ///
    /// - Parameter isEnabled: 是否启用手势，默认为true
    init(isEnabled: Bool = true) {
        self.isEnabled = isEnabled
    }

    // MARK: - ViewModifier

    func body(content: Content) -> some View {
        content
            .overlay(
                // SwiftUI手势覆盖层，专门处理NavigationStack的右滑返回
                SwiftUISwipeBackGesture(isEnabled: isEnabled)
                    .allowsHitTesting(true)
            )
    }
}

// MARK: - SwiftUI Native Swipe Back Gesture

/// SwiftUI 原生的右滑返回手势实现
///
/// 适用于 NavigationStack 等新的 SwiftUI 导航系统
/// 自动排除导航栏区域，避免与导航栏按钮冲突
private struct SwiftUISwipeBackGesture: View {
    let isEnabled: Bool
    @Environment(\.dismiss) private var dismiss
    @State private var dragOffset: CGFloat = 0
    @State private var isDragging = false

    init(isEnabled: Bool = true) {
        self.isEnabled = isEnabled
    }

    var body: some View {
        GeometryReader { geometry in
            let safeAreaTop = geometry.safeAreaInsets.top
            let navigationBarHeight = safeAreaTop + 44  // 动态计算：安全区域 + 标准导航栏高度

            HStack {
                // 左边缘手势区域，动态排除导航栏区域
                VStack {
                    // 导航栏区域 - 不接收手势
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: navigationBarHeight)
                        .allowsHitTesting(false)

                    // 实际手势区域
                    Color.clear
                        .contentShape(Rectangle())
                        .gesture(
                            DragGesture(minimumDistance: 5, coordinateSpace: .global)
                                .onChanged { value in
                                    if !isEnabled { return }

                                    let startLocation = value.startLocation

                                    // 只有从屏幕左边缘开始的手势才处理
                                    guard startLocation.x < 50 else {
                                        return
                                    }

                                    // 只处理向右的拖拽
                                    guard value.translation.width > 0 else {
                                        return
                                    }

                                if !isDragging {
                                    isDragging = true
                                }

                                dragOffset = value.translation.width
                            }
                            .onEnded { value in
                                defer {
                                    // 重置状态
                                    isDragging = false
                                    dragOffset = 0
                                }

                                if !isEnabled { return }

                                let startLocation = value.startLocation

                                // 只有从屏幕左边缘开始的手势才处理
                                guard startLocation.x < 50 else {
                                    return
                                }

                                let velocity = value.velocity.width
                                let translation = value.translation.width

                                // 判断是否应该触发返回：距离超过80px或速度超过300px/s
                                let shouldDismiss = translation > 80 || velocity > 300

                                if shouldDismiss {
                                    dismiss()
                                }
                            }
                    )
                }
                .frame(width: 50)

                // 右侧填充区域（不接收手势）
                Spacer()
                    .allowsHitTesting(false)
            }
        }
        .allowsHitTesting(true)
    }


}

// MARK: - View Extension

extension View {

    /// 启用右滑返回手势
    ///
    /// 解决使用 `navigationBarBackButtonHidden(true)` 后系统右滑返回手势被禁用的问题。
    /// 应该在设置了自定义返回按钮的视图上使用。
    /// 自动排除导航栏区域，避免与导航栏按钮冲突。
    ///
    /// - Parameter isEnabled: 是否启用手势，默认为true
    /// - Returns: 应用了右滑返回手势的视图
    ///
    /// 使用示例：
    /// ```swift
    /// ContentView()
    ///     .navigationBarBackButtonHidden(true)
    ///     .toolbar {
    ///         ToolbarItem(placement: .navigationBarLeading) {
    ///             Button("返回") { dismiss() }
    ///         }
    ///     }
    ///     .enableSwipeBackGesture()
    /// ```
    func enableSwipeBackGesture(isEnabled: Bool = true) -> some View {
        modifier(SwipeBackGestureModifier(isEnabled: isEnabled))
    }
}