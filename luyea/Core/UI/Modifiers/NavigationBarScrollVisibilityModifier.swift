import SwiftUI

// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "NavigationBar-Optimization"
//   Timestamp: "2025-07-31T09:16:18+08:00"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则), DRY (不重复代码)"
//   Quality_Check: "创建可复用的导航栏滚动控制修饰器，提升代码复用性和可维护性。"
// }}

/// 导航栏滚动显示隐藏修饰器
///
/// 基于滚动位置自动控制导航栏的显示隐藏，提供优雅的用户体验。
/// 当内容向上滚动超过指定阈值时显示导航栏，向下滚动时隐藏导航栏。
///
/// 核心特性：
/// - 可配置的滚动阈值
/// - 平滑的动画过渡
/// - 自动状态管理
/// - 与现有导航栏工具栏集成
///
/// 使用示例：
/// ```swift
/// ScrollView {
///     LazyVStack {
///         // 内容
///     }
/// }
/// .navigationBarVisibleOnScroll(threshold: 270)
/// ```
struct NavigationBarScrollVisibilityModifier: ViewModifier {
    
    // MARK: - Properties
    
    /// 滚动阈值（负值表示向上滚动的距离）
    private let threshold: CGFloat
    
    /// 动画配置
    private let animation: Animation
    
    /// 导航栏是否可见
    @State private var isNavigationBarVisible = false

    /// 可见状态绑定（可选）
    private let visibilityBinding: Binding<Bool>?
    
    // MARK: - Initialization
    
    /// 初始化导航栏滚动可见性修饰器
    ///
    /// - Parameters:
    ///   - threshold: 滚动阈值，默认为270像素
    ///   - animation: 动画配置，默认为缓入缓出0.3秒
    ///   - visibilityBinding: 可选的可见状态绑定
    init(
        threshold: CGFloat = 270,
        animation: Animation = .easeInOut(duration: 0.3),
        visibilityBinding: Binding<Bool>? = nil
    ) {
        self.threshold = threshold
        self.animation = animation
        self.visibilityBinding = visibilityBinding
    }
    
    // MARK: - ViewModifier
    
    func body(content: Content) -> some View {
        content
            .toolbarBackground(
                isNavigationBarVisible ? .visible : .hidden,
                for: .navigationBar
            )
            .onAppear {
                // 初始状态：隐藏导航栏
                isNavigationBarVisible = false
                visibilityBinding?.wrappedValue = false
            }
            .onPreferenceChange(NavigationBarScrollOffsetPreferenceKey.self) { offset in
                // 基于滚动位置判断是否显示导航栏
                let shouldShowNavigationBar = offset < -threshold

                // 只在状态真正改变时执行动画
                if shouldShowNavigationBar != isNavigationBarVisible {
                    withAnimation(animation) {
                        isNavigationBarVisible = shouldShowNavigationBar
                        // 同步到外部绑定
                        visibilityBinding?.wrappedValue = shouldShowNavigationBar
                    }
                }
            }
    }
}

// MARK: - ScrollOffset PreferenceKey

/// 滚动偏移量 PreferenceKey
///
/// 用于在视图层次结构中传递滚动偏移量信息，
/// 支持更复杂的滚动监听场景。
public struct NavigationBarScrollOffsetPreferenceKey: PreferenceKey {
    public static var defaultValue: CGFloat = 0

    public static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - View Extensions

extension View {
    
    /// 基于滚动位置控制导航栏显示隐藏
    ///
    /// 当内容向上滚动超过指定阈值时显示导航栏，
    /// 向下滚动时隐藏导航栏，提供沉浸式的浏览体验。
    ///
    /// - Parameters:
    ///   - threshold: 滚动阈值（像素），默认为270
    ///   - animation: 动画配置，默认为缓入缓出0.3秒
    ///   - visibilityBinding: 可选的可见状态绑定，用于外部监听状态变化
    /// - Returns: 应用了导航栏滚动可见性控制的视图
    ///
    /// 使用示例：
    /// ```swift
    /// @State private var isNavBarVisible = false
    ///
    /// ScrollView {
    ///     LazyVStack {
    ///         ForEach(items) { item in
    ///             ItemView(item: item)
    ///         }
    ///     }
    /// }
    /// .navigationBarVisibleOnScroll(
    ///     threshold: 270,
    ///     visibilityBinding: $isNavBarVisible
    /// )
    /// ```
    func navigationBarVisibleOnScroll(
        threshold: CGFloat = 270,
        animation: Animation = .easeInOut(duration: 0.3),
        visibilityBinding: Binding<Bool>? = nil
    ) -> some View {
        modifier(NavigationBarScrollVisibilityModifier(
            threshold: threshold,
            animation: animation,
            visibilityBinding: visibilityBinding
        ))
    }
    
    /// 基于滚动位置控制导航栏显示隐藏（高级配置）
    ///
    /// 提供更详细的配置选项，适用于需要精确控制的场景。
    ///
    /// - Parameters:
    ///   - threshold: 滚动阈值（像素）
    ///   - animationDuration: 动画持续时间（秒）
    ///   - animationCurve: 动画曲线类型
    /// - Returns: 应用了导航栏滚动可见性控制的视图
    func navigationBarVisibleOnScroll(
        threshold: CGFloat,
        animationDuration: Double = 0.3,
        animationCurve: Animation = .easeInOut
    ) -> some View {
        let animation = animationCurve.speed(1.0 / animationDuration)
        return modifier(NavigationBarScrollVisibilityModifier(
            threshold: threshold,
            animation: animation
        ))
    }

    /// 为视图内容添加滚动位置监听（直接版本）
    ///
    /// 在视图背景中添加GeometryReader来监听滚动位置变化，
    /// 直接控制导航栏的显示隐藏状态。
    ///
    /// - Parameters:
    ///   - threshold: 滚动阈值（像素），默认为270
    ///   - isVisible: 导航栏可见状态的绑定
    ///   - animation: 动画配置，默认为缓入缓出0.3秒
    /// - Returns: 带有滚动位置监听的视图
    func withNavigationBarScrollTracking(
        threshold: CGFloat = 270,
        isVisible: Binding<Bool>,
        animation: Animation = .easeInOut(duration: 0.3)
    ) -> some View {
        background(
            GeometryReader { geometry in
                Color.clear
                    .onAppear {
                        isVisible.wrappedValue = false
                    }
                    .onChange(of: geometry.frame(in: .global).minY) { _, newValue in
                        let shouldShowNavigationBar = newValue < -threshold
                        withAnimation(animation) {
                            isVisible.wrappedValue = shouldShowNavigationBar
                        }
                    }
            }
        )
    }
}
