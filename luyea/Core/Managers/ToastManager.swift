import SwiftUI
import UIKit

typealias ToastDismissCallback = () -> Void

enum ToastStyle: CaseIterable {
    case plain, success, error, warning, info
}

extension ToastStyle {
    var iconName: String {
        switch self {
        case .plain: return ""
        case .success: return "checkmark.circle.fill"
        case .error: return "xmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .info: return "info.circle.fill"
        }
    }

    var iconColor: Color {
        switch self {
        case .plain: return .clear
        case .success: return .green
        case .error: return .red
        case .warning: return .orange
        case .info: return .blue
        }
    }



    var defaultDuration: TimeInterval {
        switch self {
        case .plain, .info: return 2.5
        case .success: return 2.0
        case .warning: return 3.0
        case .error: return 3.5
        }
    }
}

private enum InternalToastType {
    case text(content: String, duration: TimeInterval)
    case customView(view: AnyView, duration: TimeInterval)

    var duration: TimeInterval {
        switch self {
        case .text(_, let duration), .customView(_, let duration):
            return duration
        }
    }
}

@MainActor
final class ToastManager: ObservableObject {
    static let shared = ToastManager()

    @Published private(set) var isDisplaying: Bool = false

    private var displayWindow: UIWindow?
    private var hostingController: UIViewController?
    private var autoHideTimer: Timer?
    private var onDismissCallback: ToastDismissCallback?

    private init() {}

    deinit {
        autoHideTimer?.invalidate()
    }

    func show(
        _ message: String,
        style: ToastStyle = .plain,
        duration: TimeInterval? = nil
    ) {
        let finalDuration = duration ?? style.defaultDuration
        let toastView = createToastView(for: message, style: style)
        let internalType = InternalToastType.customView(view: toastView, duration: finalDuration)
        displayToast(internalType, onDismiss: nil)
    }

    func show(
        _ message: String,
        style: ToastStyle = .plain,
        duration: TimeInterval? = nil,
        onDismiss: @escaping ToastDismissCallback
    ) {
        let finalDuration = duration ?? (duration == nil ? TimeInterval.greatestFiniteMagnitude : style.defaultDuration)
        let toastView = createToastView(for: message, style: style)
        let internalType = InternalToastType.customView(view: toastView, duration: finalDuration)
        displayToast(internalType, onDismiss: onDismiss)
    }

    func show<Content: View>(
        _ view: Content,
        duration: TimeInterval? = 3.0,
        onDismiss: ToastDismissCallback? = nil
    ) {
        let finalDuration = duration ?? TimeInterval.greatestFiniteMagnitude
        let internalType = InternalToastType.customView(view: AnyView(view), duration: finalDuration)
        displayToast(internalType, onDismiss: onDismiss)
    }

    func dismiss() {
        hideToast()
    }

    private func displayToast(_ toastType: InternalToastType, onDismiss: ToastDismissCallback?) {
        self.onDismissCallback = onDismiss
        cleanupPreviousToast()
        setupToastWindow()

        let toastContentView = createToastContentView(for: toastType)
        let controller = UIHostingController(rootView: toastContentView)
        controller.view.backgroundColor = .clear

        hostingController = controller
        displayWindow?.rootViewController = controller
        displayWindow?.isHidden = false
        isDisplaying = true

        scheduleAutoHideTimer(duration: toastType.duration)
    }

    /**
     * 隐藏当前显示的Toast
     *
     * 执行隐藏动画，清理资源，并触发关闭回调。
     */
    private func hideToast() {
        invalidateTimer()

        let callback = onDismissCallback
        onDismissCallback = nil

        withAnimation(.easeIn(duration: 0.2)) {
            isDisplaying = false
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) { [weak self] in
            self?.cleanupToastResources()
            callback?()
        }
    }

    private func cleanupPreviousToast() {
        invalidateTimer()
        cleanupToastResources()
    }

    private func setupToastWindow() {
        guard let windowScene = UIApplication.shared.connectedScenes
            .compactMap({ $0 as? UIWindowScene })
            .first else {
            return
        }

        displayWindow = UIWindow(windowScene: windowScene)
        displayWindow?.windowLevel = UIWindow.Level.alert + 1
        displayWindow?.backgroundColor = .clear
        displayWindow?.isUserInteractionEnabled = false
    }

    private func cleanupToastResources() {
        displayWindow?.isHidden = true
        displayWindow = nil
        hostingController = nil
    }

    private func invalidateTimer() {
        autoHideTimer?.invalidate()
        autoHideTimer = nil
    }

    private func scheduleAutoHideTimer(duration: TimeInterval) {
        guard duration != TimeInterval.greatestFiniteMagnitude else {
            return
        }

        autoHideTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) { [weak self] _ in
            Task { @MainActor in
                self?.hideToast()
            }
        }
    }

    private func createToastContentView(for toastType: InternalToastType) -> AnyView {
        let contentView: AnyView

        switch toastType {
        case .text(let content, _):
            contentView = AnyView(SimpleToastView(message: content))

        case .customView(let view, _):
            contentView = view
        }

        // 包装动画容器
        return AnyView(
            AnimatedToastContainer {
                contentView
            }
            .environmentObject(self)
        )
    }

    private func createToastView(for message: String, style: ToastStyle) -> AnyView {
        switch style {
        case .plain:
            return AnyView(SimpleToastView(message: message))
        case .success, .error, .warning, .info:
            return AnyView(StyledToastView(message: message, style: style))
        }
    }
}

private struct AnimatedToastContainer<Content: View>: View {
    let content: Content
    @EnvironmentObject private var toastManager: ToastManager

    @State private var opacity: Double = 0.0

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        content
            .opacity(opacity)
            .onAppear {
                withAnimation(.easeOut(duration: 0.2)) {
                    opacity = 1.0
                }
            }
            .onChange(of: toastManager.isDisplaying) { _, isDisplaying in
                if !isDisplaying {
                    withAnimation(.easeIn(duration: 0.2)) {
                        opacity = 0.0
                    }
                }
            }
    }
}

private struct SimpleToastView: View {
    let message: String

    var body: some View {
        VStack {
            Spacer()

            Text(message)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(3)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.85))
                )
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

private struct StyledToastView: View {
    let message: String
    let style: ToastStyle

    var body: some View {
        VStack {
            Spacer()

            HStack(spacing: 12) {
                if !style.iconName.isEmpty {
                    Image(systemName: style.iconName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(style.iconColor)
                }

                Text(message)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
                    .lineLimit(3)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.85))
            )
            .padding(.horizontal, 20)
            .padding(.bottom, 100)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}



